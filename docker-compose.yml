version: "3.8"

include:
  - database-compose.yml

x-app:
  &app-base
  build:
    context: .
    dockerfile: Dockerfile.dev # * Using Dockerfile if preferred build from RVM
    # * For application upgrading
    # args:
    #   DISTRO_VERSION: 22.04
    #   RUBY_VERSION: 2.7.8
    #   BUNDLER_VERSION: 2.3.26
  restart: unless-stopped
  image: workz_app:latest
  env_file: .env
  tty: true
  stdin_open: true
  depends_on:
    - db
    - redis
    - elasticsearch
  links:
    - mailcatcher
  volumes:
    - .:/app
    # TODO(Phuc): Remove this volume when we have a better way to cache gems
    - workz_cache_gems:/usr/local/rvm/gems/ruby-2.5.1/gems

services:
  app:
    <<: *app-base
    container_name: workz_local_web
    ports:
      - ${APP_PORT}:3000

  sidekiq:
    <<: *app-base
    container_name: workz_local_sidekiq
    command: bundle exec sidekiq -C config/sidekiq.yml

  mailcatcher:
    image: dockage/mailcatcher:0.9.0
    ports:
      - 1025:1025
      - 1080:1080

volumes:
  workz_cache_gems:
  workz_redisdata:
  workz_mysqldata:
  workz_elasticsearchdata:
  workz_elasticsearchplugins:
