FROM ubuntu:20.04

ENV DEBIAN_FRONTEND=noninteractive

ARG RUBY_VERSION=2.5.1
ARG BUNDLER_VERSION=1.17.3

# 1. Install dependencies
RUN apt-get update && apt-get install -y \
  autoconf \
  bison \
  build-essential \
  libssl-dev \
  libreadline-dev \
  zlib1g-dev \
  libncurses5-dev \
  libyaml-dev \
  libffi-dev \
  libgdbm-dev \
  git \
  curl \
  wget \
  tzdata \
  vim \
  imagemagick \
  libmysqlclient-dev \
  libnss3-tools \
  nodejs \
  libxml2-dev \
  libxslt1-dev \
  htop \
  libjemalloc2 \
  && rm -rf /var/lib/apt/lists/*

# 2. Install ruby-build
RUN git clone https://github.com/rbenv/ruby-build.git /tmp/ruby-build && \
    /tmp/ruby-build/install.sh && \
    rm -rf /tmp/ruby-build

# 3. Build & install Ruby
RUN ruby-build $RUBY_VERSION /usr/local

# 4. Add ruby & bundler to PATH
ENV PATH="/usr/local/bin:$PATH"
RUN gem install bundler -v $BUNDLER_VERSION

# 5. Create directory app
WORKDIR /app

# 6. Install gems
COPY Gemfile* /app
RUN bundle install --jobs=4 --retry=3

# 7. Copy application code
COPY . /app

EXPOSE 3000

CMD ["bundle", "exec", "rails", "server", "-b", "0.0.0.0"]
