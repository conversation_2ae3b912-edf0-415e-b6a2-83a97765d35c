class Order < ApplicationRecord
  attr_accessor :payment_basic_unit_price, :payment_night_unit_price,
    :billing_basic_unit_price, :billing_night_unit_price

  extend OrderAsSpecified
  include OrderDecorator
  include FormatBeforeValidation
  include SetLatLongDecorator
  include DomainInfo
  include TrainingScheduleConcern

  acts_as_paranoid

  MAX_ORDER_PORTION = 100
  REGULAR_ORDER_PRICE = [:payment_basic_unit_price, :payment_night_unit_price,
    :billing_basic_unit_price, :billing_night_unit_price]
  BILLING_ATTRS = [:is_use_current_billing_address, :billing_name, :billing_postal_code,
    :billing_organization_id, :billing_prefecture_id, :billing_city, :billing_street_number,
    :billing_building, :billing_tel, :billing_fax]
  ORDER_ATTRS = REGULAR_ORDER_PRICE + BILLING_ATTRS + [:id, :definition_id, :corporation_id, :corporation_group_id,
    :organization_id, :location_id, :haken_destination_pic_id, :haken_destination_pic_position,
    :haken_destination_pic_tel, :mandator_id, :mandator_position, :mandator_tel, :claim_pic_id,
    :claim_pic_position, :claim_pic_tel, :order_pic_id, :order_pic_email, :order_pic_tel,
    :overall_started_at, :overall_ended_at, :is_used_existing_pic, :type_id, :note, :status_id,
    :copied_from_id, :violation_day, :location_tel, :location_fax,
    :location_postal_code, :location_is_store_parking_area_usable, :order_segment_id, :order_template_id,
    :is_fixed_term_project, :is_limited_day, :bill_paym_temp, :training_session_code, :training_schedule_code,
    :is_violation_day_unchanged, :is_location_survey_unchanged, :is_not_one_man_operation,
    {order_branches_attributes: OrderBranch::ORDER_BRANCH_ATTRS}]

  INDEX_RESPONSE_ATTRS = [:id, :definition_id, :location_id, :status_id, :created_at, :order_segment_id]

  STEP_1_ATTRS = [:definition_id, :organization_id, :location_id,
    :haken_destination_pic_id, :haken_destination_pic_position, :haken_destination_pic_tel,
    :mandator_id, :mandator_position, :mandator_tel, :claim_pic_id, :claim_pic_position,
    :claim_pic_tel, :order_pic_id, :order_pic_email, :order_pic_tel, :is_used_existing_pic]
  ADMIN_STEP_1_ATTRS = BILLING_ATTRS + STEP_1_ATTRS + %i(corporation_id order_segment_id
    is_fixed_term_project is_limited_day training_schedule_code)
  COPIED_FIELD_LOCATION = %w(longitude latitude postal_code prefecture_id city
    street_number building tel fax email is_store_parking_area_usable)

  DETAIL_PIC_TYPE = %w(haken_destination_pic_id mandator_id claim_pic_id order_pic_id)
  TRAINING_STATUS = %w(training new_pos_training rank_up_training career_up_training)

  ORDER_DEFINE_METHODS = [:organization_full_name, :location_name, :location_full_address,
    :location_parking_area_name, :corporation_group_violation_day,
    :haken_destination_location_pic_name, :mandator_pic_name,
    :claim_location_pic_name, :order_pic_name]
  STATUSES = {
    draft: "draft",
    waiting_approved: "waiting_approved",
    op_checking: "op_checking",
    confirmed: "confirmed"
  }
  LOCATION_CODE_PREFIX = "【ダミー】<000000>"

  belongs_to :organization
  belongs_to :location
  belongs_to :corporation_group
  belongs_to :corporation
  belongs_to :business, optional: true
  belongs_to :user_order_pic, class_name: "User", foreign_key: :order_pic_id, optional: true
  belongs_to :pic_department, class_name: "Department", foreign_key: :pic_department_id,
    optional: true
  has_many :order_branches, dependent: :destroy
  has_many :order_cases
  has_many :order_portions
  has_many :staff_apply_order_cases
  has_many :arrangements, dependent: :destroy
  has_many :work_achievements, through: :arrangements
  has_many :arrange_payments, through: :arrangements
  has_many :arrange_billings, through: :arrangements
  has_many :migration_arrangement_histories, through: :arrangements
  has_many :staff_complaints, dependent: :destroy
  accepts_nested_attributes_for :order_branches, allow_destroy: true

  enum :status_id, {draft: 1, waiting_approved: 2, op_checking: 3, confirmed: 4, deny: 5}
  enum :type_id, {individual: 1, batch: 2, non_recurring: 3, recurring: 4, template: 5}
  enum :current_location_type, {normal_location: 0, new_location: 1, old_location: 3}
  enum :order_segment_id, {haken: 1, training: 2, new_pos_training: 3, rank_up_training: 4,
    career_up_training: 5, regular_order: 6, contract: 7}

  validation_with_format_text_field :note

  validates :corporation_id, :corporation_group_id, :organization_id, :location_id,
    :haken_destination_pic_id, :haken_destination_pic_position, :haken_destination_pic_tel,
    :mandator_id, :mandator_position, :mandator_tel, :claim_pic_id,
    :claim_pic_position, :claim_pic_tel, :order_pic_id, :order_pic_email,
    :order_pic_tel, :overall_started_at, :overall_ended_at, presence: true
  validates :is_used_existing_pic, inclusion: {in: [true, false]}
  validates :definition_id, length: {maximum: Settings.maxlength.corporation.order.definition_id}
  validates :haken_destination_pic_tel, :mandator_tel, :claim_pic_tel, :order_pic_tel,
    :billing_tel, format_rule: {rule: :tel}
  validates :order_pic_email, format_rule: {rule: :email}
  validates :billing_tel, length: {maximum: Settings.maxlength.common.tel}
  validates_associated :order_branches
  validates :note, length: {maximum: Settings.maxlength.order.note}

  delegate :full_name, :position_name, to: :organization, prefix: true
  delegate :name, :full_address, :code, :station_1, :station_2, :station_3, :address,
    :station_4, :station_5, :prefecture_name, :note, :short_name, :station_1_info,
    :station_2_info, :station_3_info, :station_4_info, :station_5_info,
    :stations_1_name, :stations_2_name, :stations_3_name, :stations_4_name, :stations_5_name,
    :latitude, :longitude, :tel, :stations_1_station_name, :pos_type_id, :email, :only_name,
    :prefecture_name, :location_type, :caution_to_staff_mail, :get_type, :thumbnail_path, :thumbnail_background_path,
    :logo, :job_content, :personal_things, :clothes, :special_note, :job_category_name,
    :job_categories_text, :is_export_timesheet, :station_1_short_info, :camelized_type,
    to: :location, prefix: true, allow_nil: true
  delegate :violation_day, :pic_department_id, :haken_acceptance_started_at, :full_name,
    to: :corporation_group, prefix: true
  delegate :full_name, :address, :representative_name, :tel, to: :corporation, prefix: true

  after_initialize :copied_data
  after_update :update_order_cases
  before_save :init_pic_admin_data, :add_current_location_type

  scope :updated_by_user, ->user_id do
    where("created_user_id = :user_id or rejected_user_id = :user_id or " \
      "approved_user_id = :user_id or order_pic_id = :user_id", user_id: user_id)
  end

  scope :by_corporation_groups, ->corporation_group_ids do
    where(corporation_group_id: corporation_group_ids)
  end

  scope :by_locations, ->location_ids do
    where(location_id: location_ids)
  end

  scope :by_corporations, ->corporation_ids do
    where(corporation_id: corporation_ids)
  end

  scope :by_occupations, ->occupation_ids do
    where(occupation_id: occupation_ids)
  end

  scope :by_organizations, ->organization_ids do
    where(organization_id: organization_ids)
  end

  scope :by_businesses, ->business_ids do
    where(business_id: business_ids)
  end

  scope :by_order_segments, ->order_segment_ids do
    where(order_segment_id: order_segment_ids)
  end

  scope :by_statuses, ->status_ids do
    where(status_id: status_ids)
  end

  scope :exist_order_less_than_closed_at, ->(closed_at) do
    joins(:arrangements).where("arrangements.working_started_at > ? AND
      arrangements.working_started_at < ? AND arrangements.display_status_id = ?",
      closed_at, ServerTime.now + 2.hours, Arrangement.display_status_ids[:arranged])
  end

  scope :created_in_range, ->(start_time, end_time) do
    where("orders.created_at >= ? AND orders.created_at <= ?", start_time, end_time)
  end

  # Deprecated (TinhDT): Use copied_data to init data about location
  # def check_lat_long
  #   return unless self.location.present? || self.location_latitude_changed? ||
  #     self.location_longitude_changed? || self.location_prefecture_id_changed? ||
  #     self.location_city_changed? || self.location_street_number_changed? ||
  #     self.location_building.changed? || self.location_postal_code_changed?
  #   set_lat_long
  # end

  # Deprecated (TinhDT): Use copied_data to init data about location
  # def set_lat_long
  #   search_result = Geocoder.search([location_latitude, location_longitude])
  #   result = search_result.first.coordinates if search_result.present?
  #   result = set_lat_long_value(full_address, location_prefecture_name, location_postal_code) if result.blank?
  #   self.location_latitude, self.location_longitude = result
  # end

  # Deprecated (TinhDT): Unused methods.
  # Note: location_latitude and location_longitude are column in table `orders`
  #   but they are overwritten by delegation method from `locations`
  # def get_coordinates
  #   [self.location_latitude, self.location_longitude]
  # end

  def total_order_portions
    self.confirmed? ? order_portions_count : estimate_total_portion
  end

  def update_total_order_portions
    self.update_column(:order_portions_count, self.order_portions.valid_status.count)
    # self.reindex_now unless reindex_later # (Deprecated by OrderSearch)
  end

  def copied_data
    return if !self.new_record? || self.is_migrated

    change_params_data
  end

  def should_create_owner_request_notification? previous_status
    previous_status == "draft" && self.status_id == "waiting_approved"
  end

  def should_create_owner_approve_notification? previous_status
    previous_status == "waiting_approved" && self.status_id == "confirmed"
  end

  def op_center_should_create_notification? previous_status
    previous_status.in?(%w(waiting_approved op_checking)) && self.status_id == "confirmed"
  end

  def should_delete_owner_notification? previous_status
    previous_status == "waiting_approved" && status_id.in?(%w(confirmed draft))
  end

  def should_authority_owner_create_notification? user
    self.confirmed? && user_confirm_order?(user)
  end

  def should_owner_update_order_create_notification? user
    (self.confirmed? || self.draft?) && user_confirm_order?(user)
  end

  def user_confirm_order? user
    !(user.normal? && self.location.is_approval_required)
  end

  def init_pic_admin_data
    return if self.is_migrated

    derpartment_pics = self.pic_department&.department_pics
    return if derpartment_pics.blank?

    pic_type2 = get_derpartment_pic(derpartment_pics, Settings.order.pic_types.haken)
    pic_type4 = get_derpartment_pic(derpartment_pics, Settings.order.pic_types.claim_name)
    self.haken_source_pic_name = pic_type2&.name
    self.haken_source_pic_position = pic_type2&.position
    self.claim_process_pic_name = pic_type4&.name
    self.claim_process_pic_position = pic_type4&.position
  end

  def is_from_lawson?
    self.corporation.is_lawson?
  end

  def is_haken_type_labor?
    self.corporation.labor?
  end

  def created_user
    User.find_by(id: self.created_user_id)
  end

  def calculate_overall_attributes
    new_order_branches = self.order_branches.reject do |ob|
      ob.marked_for_destruction? || ob.started_at.nil? || ob.ended_at.nil?
    end

    self.overall_started_at = new_order_branches.min_by(&:started_at)&.started_at
    self.overall_ended_at = new_order_branches.max_by(&:ended_at)&.ended_at
    self.estimate_total_portion = new_order_branches.sum{|ob| ob.estimate_total_portion.to_i}
  end

  def valid_estimate_total_portion?
    self.estimate_total_portion <= MAX_ORDER_PORTION
  end

  def location_parking_area_name
    I18n.t("corporation.order.store_parking.#{self.location_is_store_parking_area_usable}")
  end

  def full_address
    [location_prefecture_name, location_city, location_street_number,
      location_building].compact.join
  end

  # Deprecated (TinhDT): Unused method
  # def get_name_location_pic location_pic_id
  #   LocationPic.where(id: location_pic_id).first.name
  # end

  def able_update_by? ids
    ids.include? self.location_id
  end

  def to_json_detail user = nil, copy = nil
    location_pics = OrderLocationDataService.new(self.location_id).pics_data
      .reject{|k, _v| k == :error_message}
    if copy.to_s.true?
      order = self.as_json(except: :id, methods: :organization_full_name)
    else
      order = self.as_json(only: ORDER_ATTRS, methods: [:full_address, :organization_full_name])
    end
    locations = user ? user.locations : Location
    locations = locations.includes(:prefecture).by_organization_id(self.corporation.organizations.pluck(:id))
    {
      order: order,
      location_pics: location_pics,
      locations: locations.as_json(only: Location::ORDER_ATTR_JSON, methods: :full_address),
      is_lawson_staff: corporation.is_lawson_staff
    }.to_json
  end

  def haken_destination_location_pic_name
    find_name_from_location_pics(haken_destination_pic_id, haken_destination_pic_position,
      haken_destination_pic_tel)
  end

  def mandator_pic_name
    find_name_from_location_pics(mandator_id, mandator_position, mandator_tel)
  end

  def claim_location_pic_name
    find_name_from_location_pics(claim_pic_id, claim_pic_position, claim_pic_tel)
  end

  def order_pic_name
    user_name = User.find_by(id: order_pic_id)&.name
    [user_name, order_pic_email, order_pic_tel].join(" ")
  end

  def order_pic_user_name
    user_order_pic&.name
  end

  # Deprecated (TinhDT): Unused method
  # def should_index?
  #   self.deleted_at.blank?
  # end

  def haken_period
    return if self.overall_started_at.blank? || self.overall_ended_at.blank?

    from_date = DateTimeFormatting.full_date_and_day(self.overall_started_at)
    to_date = DateTimeFormatting.full_date_and_day(self.overall_ended_at)

    "#{from_date}~#{to_date}"
  end

  # TODO(TinhDT): THis method should be refactored to other service
  def send_mail_change_status is_send_order, order_action, user
    mail_type = :approve if is_send_order && created_user&.normal?
    mail_type = :cancel if order_action == "repayment" && !user.normal?
    if order_action == "waiting_approved"
      SendMailRequestApproveWorker.perform_async(self.id, user.id)
    elsif mail_type
      UserMailer.send_mail_order(user, self, mail_type).deliver_now
    end
  end

  def exported_label
    exported = self.is_exported ? "exported" : "not_exported"
    I18n.t("admin.order.order_list_page.export_status.#{exported}")
  end

  def business_name
    location_job_categories_text
  end

  def pic_department_name
    location&.department&.name
  end

  def real_pic_department_name
    pic_department&.name
  end

  def segment_name
    return "" unless order_segment_id

    I18n.t("corporation.order.segment_ids.#{order_segment_id}")
  end

  def id_with_leading_zeros
    self.id.to_s.rjust(Settings.num_leading_zeros.order_id, "0")
  end

  def order_created_at
    return if self.draft?

    I18n.l self.created_at, format: Settings.date.day_and_date
  end

  def change_params_data
    return if self.organization_id.nil? || self.location_id.nil?

    self.corporation_group_id = self.organization.corporation_group_id
    self.corporation_id = self.corporation_group.corporation_id
    self.violation_day = self.corporation_group.violation_day
    set_data_by_location
    set_location_pic_name(self.location)
  end

  def location_code_format
    "#{LOCATION_CODE_PREFIX}#{location_code}"
  end

  def export_location_address
    "#{location_prefecture_name}#{location_city}#{location_street_number}"
  end

  def haken_source_pic_tel
    self.corporation_group.pic_department.address_tel
  end

  def set_pic_department_id
    self.pic_department_id = self.location.department_id
  end

  def recurring_order
    self.recurring?
  end

  def recurring_working_time
    selected_days = self.order_branches.first.get_selected_days.map do |day|
      Settings.date.weekdays[day]
    end
    return if selected_days.empty?

    "(#{selected_days.join(' ')})"
  end

  def overall_working_dates
    from = DateTimeFormatting.full_date(self.overall_started_at)
    to = DateTimeFormatting.full_date(self.overall_ended_at)

    "#{from} ~ #{to}"
  end

  def regular_order_timeout_apply?
    return unless self.regular_order? && self.last_started_at

    self.last_started_at <= ServerTime.now
  end

  # Deprecated (TinhDT): Unused method
  # def is_from_template?
  #   self.order_template_id.present?
  # end

  def is_created_by_owner?
    self.created_user_id.present?
  end

  def skip_staff_violation?
    !self.haken? || self.is_fixed_term_project? || self.is_limited_day?
  end

  def used_payment_billing_template?
    arrangements&.any?{|arrangement| arrangement.billing_payment_template_id.present?}
  end

  def created_full_date
    I18n.l self.created_at, format: Settings.date.full_date_time
  end

  class << self
    def status_id_options order_type
      pre_status = "admin.order.order_list_page.status_ids"
      pre_status = "corporation.order.status_ids" if order_type == "corporation"
      status_ids.map do |key, val|
        {
          id: val,
          key: key,
          name: I18n.t("#{pre_status}.#{key}")
        }
      end
    end

    def order_segment_options
      order_segment_ids.map do |key, val|
        {
          id: val,
          key: key,
          name: I18n.t("corporation.order.segment_ids.#{key}")
        }
      end
    end

    def regular_order_segment_options
      [{id: order_segment_ids[:regular_order], key: "regular_order",
        name: I18n.t("corporation.order.segment_ids.regular_order")}]
    end
  end

  private
  def add_current_location_type
    return if self.draft? || self.location_id.blank?
    return unless self.new_record? || self.status_id_before_last_save == "draft"

    last_order = Order.where(location_id: self.location_id)
      .where.not(status_id: :draft).order(created_at: :ASC).last
    return self.current_location_type = :new_location if last_order.blank?

    self.current_location_type = :old_location if last_order.created_at < ServerTime.now - 30.days
  end

  def get_derpartment_pic derpartment_pics, pic_id
    derpartment_pics.find{|pic| pic.department_pic_type_id == pic_id}
  end

  def find_name_from_location_pics pic_id, position, tel
    pic = location_pic_by_ids.find{|l_p| l_p.id == pic_id}
    [pic&.name, position, tel].join(" ")
  end

  def location_pic_by_ids
    pic_id = self.slice(*DETAIL_PIC_TYPE).values
    @location_pic_by_ids ||= LocationPic.where(id: pic_id)
  end

  def set_data_by_location
    COPIED_FIELD_LOCATION.each do |field|
      self["location_#{field}"] = self.location.send(field)
    end
  end

  def set_location_pic_name location
    location_pics = location.location_pics.index_by(&:id)

    self.haken_destination_pic_name = location_pics[self.haken_destination_pic_id]&.name
    self.mandator_name = location_pics[self.mandator_id]&.name
    self.claim_pic_name = location_pics[self.claim_pic_id]&.name
  end

  def update_order_cases
    return unless self.confirmed?

    order_branches.each &:create_order_cases
    self.update_total_order_portions
  end
end
