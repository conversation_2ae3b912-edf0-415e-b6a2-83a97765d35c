class JobsWage::CacheJobsWagesService
  SORT_FIELDS = %w(display_unit_price display_total_price average_price case_started_at)
  DEFAUT_FILTER_RANGE = [0, 24]
  DELAY_CACHING_SECONDS = 5
  def initialize date, order_case_ids
    @date = date
    @cache_key = "wages_#{date}"
    @redis = Lawson::RedisConnector.new(Settings.redis.db.location_job)
    @order_case_ids = order_case_ids
  end

  def get_wages
    find_jobs_wages
    get_wages_by_ids
  end

  class << self
    def clear_cache_for_all_peak_period
      PeakPeriod.pluck(:target_date).each.map do |date|
        date_format = date.strftime(Settings.date.non_separator)
        JobsWage::CacheJobsWagesService.delete_cache(date_format)
      end
      CachingLocationJobsDataService.delete_all_cache
    end

    def reload_cache order_case
      order_case_date = order_case&.case_started_at&.beginning_of_day
      date = order_case_date&.strftime(Settings.date.non_separator)
      return if date.blank?

      redis = Lawson::RedisConnector.new(Settings.redis.db.location_job)
      RedisWorkerCachingService.set_order_case(order_case, redis)
      order_case_ids = RedisWorkerCachingService.get_order_case_ids(date, redis)
      cache_wages = JobsWage::CacheJobsWagesService.get_cache(date, redis)
      if cache_wages.present?
        new_wages = JobsWage::CacheForCalculation.add_order_cases_wages_to_wages(cache_wages, order_case_ids)
      else
        new_wages = JobsWage::CacheForCalculation.wages_by_date(order_case_date)
      end
      JobsWage::CacheJobsWagesService.save_cache(date, new_wages)
    end

    def save_cache date, standard_wages
      jid = CacheWagesByDateWorker.perform_in(DELAY_CACHING_SECONDS.seconds,
        date, standard_wages)
      RedisWorkerCachingService.write_log("ADD SIDEKIQ #{jid}")
      caching_worker_key = RedisWorkerCachingService.new(jid, "sidekiq_wages", date)
      caching_worker_key.save_worker_key_to_redis
      caching_worker_key.keep_last_worker_and_cancel_all
    end

    def get_cache date, redis = nil
      data = RedisWorkerCachingService.get_cache(cache_key(date), redis)
      data.present? ? JSON.parse(data) : data
    end

    def delete_all_cache
      RedisWorkerCachingService.delete_similar_caches("wages_*")
    end

    def delete_cache date, redis = nil
      RedisWorkerCachingService.delete_cache(cache_key(date), redis)
    end

    def set_cache date, standard_wages, redis = nil
      redis ||= Lawson::RedisConnector.new(Settings.redis.db.location_job)
      cache_key = cache_key(date)
      formated_wages = format_save_wages(standard_wages.uniq).to_json
      RedisWorkerCachingService.set_cache(cache_key, formated_wages, total_expired_seconds(date), redis)
      RedisWorkerCachingService.delete_order_cases_queue(date, redis)
    end

    private
    def total_expired_seconds date
      end_day = date.in_time_zone(ServerTime.name).end_of_day
      end_day.to_i - ServerTime.now.to_i
    end

    def format_save_wages wages
      formated_wages = {}
      wages.each do |wage|
        oc_id = wage.symbolize_keys[:id]
        formated_wages[oc_id] = wage
      end
      formated_wages
    end

    def cache_key date
      "wages_#{date}"
    end
  end

  private

  def find_jobs_wages
    @all_wages_formated = JobsWage::CacheJobsWagesService.get_cache(@date, @redis)
    if @all_wages_formated.present?
      @all_wages = @all_wages_formated.values.flatten
      @is_new_cache = false
      return @all_wages
    end
    order_cases_options = {
      order_case_ids: @order_case_ids,
      with_location_id: true
    }
    wages = JobsWage::CacheForCalculation.new(order_cases_options, nil)
    @is_new_cache = true
    @all_wages = wages.calculate
    save_to_redis(@all_wages)
    @all_wages
  end

  def get_wages_by_ids
    return @all_wages if @is_new_cache

    @all_wages_formated ||= {}
    cache_oc_ids = @all_wages_formated.keys.map(&:to_i)
    diff_oc_ids = (@order_case_ids - cache_oc_ids).uniq
    if diff_oc_ids.present?
      new_jobs_wages = []
      order_cases_options = {
        order_case_ids: diff_oc_ids,
        with_location_id: true
      }
      wages = JobsWage::CacheForCalculation.new(order_cases_options, nil)
      new_wages = wages.calculate
      new_jobs_wages << new_wages
      new_jobs_wages << @all_wages
      @all_wages = new_jobs_wages.flatten.uniq
      save_to_redis(@all_wages)
    end
    jobs_wages = []
    @all_wages.each do |wage|
      jobs_wages << wage if @order_case_ids.include?(wage.symbolize_keys[:id])
    end
    jobs_wages
  end

  def save_to_redis standard_wages
    JobsWage::CacheJobsWagesService.save_cache(@date, standard_wages)
  end
end
