class StaffApplyOrderCases::RejectAppliedCommand
  attr_reader :staff_apply_order_case_ids, :options, :is_sent_rejected_email

  DEFAULT_TYPE = %w(admin_rejected full_portion owner_cancelled)

  def initialize staff_apply_order_case_ids = [], options = {}, is_sent_rejected_email = true
    @staff_apply_order_case_ids = staff_apply_order_case_ids
    @options = options.symbolize_keys
    @is_sent_rejected_email = is_sent_rejected_email
  end

  def perform
    return if staff_apply_order_case_ids.blank?

    raise(CustomExceptions::InvalidTypeAction, "Type action is invalid") unless
      DEFAULT_TYPE.include?(options[:action_type])

    @staff_apply_order_cases = StaffApplyOrderCase.includes(staff: :account, order_case: [order: :location])
      .default_unscope
      .where(id: staff_apply_order_case_ids)

    case options[:action_type]
    when "owner_cancelled"
      handle_when_owner_cancelled
    end

    @staff_apply_order_cases
  end

  private

  def handle_when_owner_cancelled
    @staff_apply_order_cases.update_all(
      is_cancel: true,
      is_sent_rejected_email: is_sent_rejected_email,
      status_id: :rejected
    )

    order_case_ids = @staff_apply_order_cases.pluck(:order_case_id).uniq
    OrderCases::UpdateApplyCountWorker.perform_async(order_case_ids)

    app_notification_options = []
    @staff_apply_order_cases.each do |saoc|
      staff = saoc.staff
      account = staff&.account
      order_case = saoc.order_case

      create_arrange_log([saoc.order_case_id], ArrangeLog.action_types[:oc_rejected], staff.id, options[:user_id])
      next if order_case.cancel? || !is_sent_rejected_email

      location_name = saoc.order&.location&.name
      send_notifications(staff, account, saoc, location_name)
      app_notification_options << new_notification_option(saoc, location_name)
    end

    app_notification_options.in_groups_of(Settings.app_notification.batch_size).each do |notification_options|
      AppSendNotificationWorker.perform_async(notification_options.reject(&:blank?))
    end

    write_log_owner_cancelled
  end

  def write_log_owner_cancelled
    logger = Logger.new Rails.root.join("log", "reject_apply.log")
    content = "\nStart time: #{ServerTime.now}, owner cancelled\n"
    content << "\nRejected staff_apply_order_cases with ID: #{@staff_apply_order_cases.pluck(:id).join(', ')}\n"
    content << "End time: #{ServerTime.now}, owner cancelled\n"
    logger.info content
  end

  def send_notifications staff, account, staff_apply_oc, location_name, is_registration_training_job = false
    return if is_registration_training_job

    if account&.email.present?
      options = {
        staff_apply_oc: staff_apply_oc,
        is_registration_training_job: is_registration_training_job
      }
      Notification::SentEmailToStaffService.new(staff, :reject_staff_apply_mail, options).execute
    elsif account&.tel.present?
      MessageSenderService.send_rejected_staff_apply_message(
        account.tel,
        location_name,
        is_registration_training_job
      )
    end
  end

  def new_notification_option staff_apply_oc, location_name, is_registration_training_job = false
    {
      staff_id: staff_apply_oc.staff_id,
      order_case_id: staff_apply_oc.order_case_id,
      arrangement_id: staff_apply_oc.arrangement_id,
      creator_type: :by_system,
      params: {location_name: location_name},
      notification_type: is_registration_training_job ? :rejected_training_job : :rejected_job
    }
  end

  def create_arrange_log order_case_id, action_type, staff_id, user_id
    cmd = ImportDataArrangeLogService.new([order_case_id], action_type, nil, staff_id, user_id)
    cmd.import_data
  end
end
