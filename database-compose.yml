version: "3.8"

services:
  db:
    image: mysql:${DB_VERSION}
    container_name: workz_local_db
    restart: unless-stopped
    env_file: .env
    volumes:
      - ${DB_VOLUME_NAME}:${DB_CONTAINER_DATA}
    command: --default-authentication-plugin=mysql_native_password
    ports:
      - ${DB_PORT}:3306

  redis:
    image: redis:${REDIS_VERSION}
    container_name: workz_local_redis
    restart: unless-stopped
    command:
      [
        "redis-server",
        "--save",
        "60",
        "1",
        "--loglevel",
        "warning"
      ]
    env_file: .env
    volumes:
      - ${REDIS_VOLUME_NAME}:${REDIS_CONTAINER_DATA}
    ports:
      - ${REDIS_PORT}:6379

  elasticsearch:
    # platform: linux/amd64
    image: elasticsearch:${ES_VERSION}
    container_name: workz_local_elasticsearch
    restart: on-failure
    command:
      - sh
      - -c
      - "elasticsearch-plugin list | grep -q analysis-kuromoji || elasticsearch-plugin install analysis-kuromoji;
        /docker-entrypoint.sh elasticsearch"
    environment:
      - cluster.name=docker-cluster
      - bootstrap.memory_lock=true
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xmx2048m -Xms2048m"
    env_file: .env
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - ${ES_VOLUME_NAME}:${ES_CONTAINER_DATA}
      - ${ES_PLUGIN_VOLUME_NAME}:${ES_CONTAINER_PLUGINDATA}
    ports:
      - ${ES_PORT}:9200
      - ${ES_PORT2}:9300
