GIT
  remote: https://git.ponos-tech.com/public-libraries/turnout
  revision: 2fdaa666f21f120c7cdadf2d662083d5c2119f31
  specs:
    turnout (2.5.0)
      i18n (>= 0.7, < 2)
      rack (>= 1.3, < 3)
      rack-accept (~> 0.4)
      tilt (>= 1.4, < 3)

GIT
  remote: https://github.com/jpgeek/unicode_japanese.git
  revision: ea05a15bad3ec32174affec9c542fa00d5c1eb4b
  specs:
    unicode_japanese (0.2)

GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.5.1)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (2.2.0)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    airbrussh (1.5.3)
      sshkit (>= 1.6.1, != 1.7.0)
    ast (2.4.3)
    awesome_print (1.9.2)
    aws-eventstream (1.4.0)
    aws-partitions (1.1129.0)
    aws-sdk-autoscaling (1.138.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-cloudfront (1.119.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.226.2)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-ec2 (1.538.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-kms (1.106.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.193.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-ses (1.85.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-sqs (1.96.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    blueprinter (0.25.3)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    bootstrap-kaminari-views (0.0.5)
      kaminari (>= 0.13)
      rails (>= 3.1)
    brakeman (4.9.0)
    browser (5.3.1)
    builder (3.3.0)
    bullet (8.0.8)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    byebug (11.1.3)
    cancancan (3.6.1)
    capistrano (3.19.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.1)
      capistrano (~> 3.1)
    capistrano-rails (1.7.0)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-resque (0.2.3)
      capistrano
      resque
      resque-scheduler
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capistrano-sidekiq (3.0.0)
      capistrano (>= 3.9.0)
      capistrano-bundler
      sidekiq (>= 6.0.6)
    capistrano3-puma (5.2.0)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (>= 4.0, < 6.0)
    carrierwave (1.3.4)
      activemodel (>= 4.0.0)
      activesupport (>= 4.0.0)
      mime-types (>= 1.16)
      ssrf_filter (~> 1.0, < 1.1.0)
    carrierwave-aws (1.4.0)
      aws-sdk-s3 (~> 1.0)
      carrierwave (>= 0.7, < 2.1)
    caxlsx (4.1.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    childprocess (5.1.0)
      logger (~> 1.5)
    chronic_duration (0.10.6)
      numerizer (~> 0.1.1)
    cloudfront-signer (3.0.2)
    cocoon (1.2.15)
    code_analyzer (0.5.5)
      sexp_processor
    coffee-rails (4.2.2)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.3.5)
    config (5.6.1)
      deep_merge (~> 1.2, >= 1.2.1)
      ostruct
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    creek (2.6.3)
      nokogiri (>= 1.10.0)
      rubyzip (>= 1.0.0)
    cropper-rails (*******)
      railties (>= 3.1.0)
    csv (3.3.5)
    database_cleaner (2.1.0)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    deep_cloneable (3.2.1)
      activerecord (>= 3.1.0, < 9)
    deep_merge (1.2.2)
    diff-lcs (1.6.2)
    docile (1.4.1)
    domain_name (0.6.20240107)
    drb (2.2.3)
    erb (5.0.2)
    erubi (1.13.1)
    erubis (2.7.0)
    et-orbi (1.2.11)
      tzinfo
    ethon (0.16.0)
      ffi (>= 1.15.0)
    exception_notification (4.6.0)
      actionmailer (>= 5.2, < 9)
      activesupport (>= 5.2, < 9)
    execjs (2.10.0)
    factory_bot (6.4.5)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (2.23.0)
      i18n (>= 1.8.11, < 2)
    faraday (2.8.1)
      base64
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-net_http (3.0.2)
    fast_excel (0.5.0)
      ffi (> 1.9, < 2)
    fcm (2.0.1)
      faraday (>= 1.0.0, < 3.0)
      googleauth (~> 1)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    foreman (0.88.1)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gaffe (1.2.0)
      rails (>= 4.0.0)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    googleauth (1.11.2)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    hashdiff (1.2.0)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    i18n-js (3.9.2)
      i18n (>= 0.6.6)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    janus-ar (7.2.2)
      activerecord (~> 7.2)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.13.0)
    jwt (2.10.2)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kwalify (0.7.2)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    memory_profiler (1.0.2)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0715)
    mini_magick (5.3.0)
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    mono_logger (1.1.2)
    msgpack (1.8.0)
    multi_json (1.15.0)
    mustermann (3.0.3)
      ruby2_keywords (~> 0.0.1)
    mysql2 (0.5.6)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    net_http_timeout_errors (1.1.0)
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    numerizer (0.1.1)
    oj (3.16.11)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    order_as_specified (1.7)
      activerecord (>= 5.0.0)
    os (1.1.4)
    ostruct (0.6.3)
    parallel (1.27.0)
    parallel_tests (4.7.1)
      parallel
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pdfjs_viewer-rails (0.3.2)
      json (> 1.8.4)
      rails (> 4.2.0)
      sassc-rails (>= 2.1)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (5.1.1)
    puma (4.3.12)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-accept (0.4.5)
      rack (>= 0.4)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails_best_practices (1.23.2)
      activesupport
      code_analyzer (~> 0.5.5)
      erubis
      i18n
      json
      require_all (~> 3.0)
      ruby-progressbar
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    redis (5.4.1)
      redis-client (>= 0.22.0)
    redis-actionpack (5.5.0)
      actionpack (>= 5)
      redis-rack (>= 2.1.0, < 4)
      redis-store (>= 1.1.0, < 2)
    redis-client (0.25.1)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-rack (3.0.0)
      rack-session (>= 0.2.0)
      redis-store (>= 1.2, < 2)
    redis-store (1.11.0)
      redis (>= 4, < 6)
    reek (6.1.4)
      kwalify (~> 0.7.0)
      parser (~> 3.2.0)
      rainbow (>= 2.0, < 4.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    require_all (3.0.0)
    resque (2.7.0)
      mono_logger (~> 1)
      multi_json (~> 1.0)
      redis-namespace (~> 1.6)
      sinatra (>= 0.9.2)
    resque-scheduler (4.11.0)
      mono_logger (~> 1.0)
      redis (>= 3.3)
      resque (>= 1.27)
      rufus-scheduler (~> 3.2, != 3.3)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.4.1)
    rouge (4.5.2)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-collection_matchers (1.2.1)
      rspec-expectations (>= 2.99.0.beta1)
    rspec-core (3.13.5)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rubocop (1.59.0)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.30.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.30.0)
      parser (>= *******)
    rubocop-checkstyle_formatter (0.6.0)
      rubocop (>= 1.14.0)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    rufus-scheduler (3.9.2)
      fugit (~> 1.1, >= 1.11.1)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sass-rails (5.0.8)
      railties (>= 5.2.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.1)
    serviceworker-rails (0.6.0)
      railties (>= 3.1)
    sexp_processor (4.17.3)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-status (3.0.3)
      chronic_duration
      sidekiq (>= 6.0, < 8)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.2)
    simplecov_json_formatter (0.1.4)
    sinatra (3.2.0)
      mustermann (~> 3.0)
      rack (~> 2.2, >= 2.2.4)
      rack-protection (= 3.2.0)
      tilt (~> 2.0)
    sprockets (3.7.5)
      base64
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    ssrf_filter (1.0.8)
    stringio (3.1.7)
    strip_attributes (2.0.0)
      activemodel (>= 3.0, < 9.0)
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.3.2)
    tilt (2.6.1)
    timecop (0.9.10)
    timeout (0.4.3)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uglifier (4.2.1)
      execjs (>= 0.3.0, < 3)
    unicode-display_width (2.6.0)
    uniform_notifier (1.17.0)
    useragent (0.16.11)
    warden (1.2.9)
      rack (>= 2.0.9)
    warden-rspec-rails (0.3.0)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wicked_pdf (2.8.2)
      activesupport
      ostruct
    wkhtmltopdf-binary (********)
    zeitwerk (2.7.3)
    zip-zip (0.3)
      rubyzip (>= 1.0.0)

PLATFORMS
  aarch64-linux
  arm64-darwin-22
  arm64-darwin-24
  x86_64-linux

DEPENDENCIES
  aasm
  activerecord-import
  awesome_print
  aws-sdk-autoscaling (~> 1)
  aws-sdk-cloudfront (~> 1)
  aws-sdk-ec2 (~> 1)
  aws-sdk-s3 (~> 1)
  aws-sdk-ses (~> 1)
  aws-sdk-sqs (~> 1)
  bcrypt
  better_errors
  blueprinter (~> 0.25.3)
  bootsnap
  bootstrap-kaminari-views
  brakeman
  browser
  bullet
  bundler-audit
  byebug
  cancancan
  capistrano
  capistrano-bundler
  capistrano-rails
  capistrano-resque
  capistrano-rvm
  capistrano-sidekiq
  capistrano3-puma
  carrierwave
  carrierwave-aws
  caxlsx
  cloudfront-signer
  cocoon
  coffee-rails (~> 4.2)
  config
  creek
  cropper-rails
  database_cleaner
  deep_cloneable
  exception_notification
  factory_bot_rails
  faker (~> 2.19)
  fast_excel
  fcm
  foreman
  gaffe
  geocoder
  googleauth
  i18n-js (~> 3.9.2)
  janus-ar
  jquery-rails
  jwt
  kaminari
  letter_opener
  listen (~> 3.9.0)
  memory_profiler
  mini_magick
  mysql2
  net_http_timeout_errors
  oj
  order_as_specified
  parallel_tests
  paranoia
  pdfjs_viewer-rails
  puma (~> 4.0)
  rack-mini-profiler (~> 3.0)
  rails (~> *******)
  rails-controller-testing
  rails_best_practices
  ransack
  redis-actionpack
  reek
  resque
  resque-scheduler
  rest-client
  rspec
  rspec-collection_matchers
  rspec-rails
  rubocop
  rubocop-checkstyle_formatter
  rubyzip
  sass-rails (~> 5.0.4)
  serviceworker-rails
  shoulda-matchers
  sidekiq
  sidekiq-status
  simplecov
  sprockets-rails
  strip_attributes
  terminal-table
  timecop
  turnout!
  typhoeus
  tzinfo-data
  uglifier (>= 1.3.0)
  unicode_japanese!
  warden
  warden-rspec-rails
  web-console
  webmock
  wicked_pdf
  wkhtmltopdf-binary
  zip-zip

RUBY VERSION
   ruby 3.2.8p263

BUNDLED WITH
   2.3.27
