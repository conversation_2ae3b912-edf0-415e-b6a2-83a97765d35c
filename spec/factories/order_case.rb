require "faker"

FactoryBot.define do
  factory :order_case do
    order{FactoryBot.build_stubbed(:order)}
    order_branch{FactoryBot.build_stubbed(:order_branch)}
    segment_id{"haken"}
    case_started_at{ServerTime.now}
    case_ended_at{ServerTime.now + 5.hours}
    special_offer_fee{0}
    total_portion{1}

    trait :skip_validate do
      to_create{|instance| instance.save(validate: false)}
    end

    trait :with_order do
      after(:build) do |oc|
        o = create(:order, overall_started_at: oc.case_started_at, overall_ended_at: oc.case_started_at)
        ob = create(:order_branch, order_id: o.id)

        oc.order = o
        oc.order_branch = ob
      end
    end
  end

  factory :past_order_case, parent: :order_case do
    case_started_at{"2020-01-01 08:00:00".in_time_zone}
    case_ended_at{"2020-01-01 13:00:00".in_time_zone}
    special_offer_fee{0}

    trait :multi_day do
      case_started_at{"2020-01-01 22:00:00".in_time_zone}
      case_ended_at{"2020-01-02 03:00:00".in_time_zone}
    end
  end

  factory :future_order_case, parent: :order_case do
    case_started_at{ServerTime.now + 3.days}
    case_ended_at{ServerTime.now + 3.days + 5.hours}
  end

  factory :multi_portion_order_case, parent: :future_order_case do
    total_portion{rand(1..2)}

    trait :full_arranged do
      after(:create) do |oc|
        (1..oc.total_portion).to_a.each do |portion|
          order_portion = create(
            :order_portion, :arranged, :with_arrangement_arranged,
            order_case: oc,
            order_id: oc.order_id,
            order_branch_id: oc.order_branch_id
          )

          arrangement = order_portion.arrangement

          FactoryBot.create(
            :staff_apply_order_case,
            :arranged,
            order_case: oc,
            order_id: oc.order_id,
            order_branch_id: oc.order_branch_id,
            arrangement: arrangement
          )
        end
      end
    end

    trait :not_arranged do
      after(:create) do |oc|
        (1..oc.total_portion).to_a.each do |portion|
          create(
            :order_portion, :not_arranged, :with_arrangement_not_arranged,
            order_case: oc,
            order_id: oc.order_id,
            order_branch_id: oc.order_branch_id
          )
        end
      end
    end
  end
end
