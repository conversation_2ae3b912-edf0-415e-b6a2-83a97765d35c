require "faker"

FactoryBot.define do
  factory :order_portion do
    order{FactoryBot.build_stubbed(:order)}
    order_branch{FactoryBot.build_stubbed(:order_branch)}
    order_case{FactoryBot.build_stubbed(:order_case)}
    case_started_at{Time.now}
    case_ended_at{Time.now}

    trait :not_arranged do
      status_id{1}
    end

    trait :arranged do
      status_id{3}
    end

    trait :with_arrangement_not_arranged do
      after(:create) do |op|
        create(
          :arrangement,
          order_id: op.order_id,
          order_branch_id: op.order_branch_id,
          order_case_id: op.order_case_id,
          order_portion: op
        )
      end
    end

    trait :with_arrangement_arranged do
      after(:create) do |op|
        create(
          :arrangement, :arranged,
          order_id: op.order_id,
          order_branch_id: op.order_branch_id,
          order_case_id: op.order_case_id,
          order_portion: op
        )
      end
    end

    trait :with_order do
      after(:build) do |arr|
        o = create(:order, overall_started_at: arr.working_started_at, overall_ended_at: arr.working_ended_at)
        ob = create(:order_branch, order_id: o.id)
        oc = create(
          :order_case, order_id: o.id, order_branch_id: ob.id,
          case_started_at: arr.working_started_at,
          case_ended_at: arr.working_ended_at
        )
        op = create(
          :order_portion, order_id: o.id, order_branch_id: ob.id, order_case_id: oc.id,
          case_started_at: arr.working_started_at,
          case_ended_at: arr.working_ended_at
        )

        arr.order = o
        arr.order_branch = ob
        arr.order_case = oc
        arr.order_portion = op
      end
    end

    # ! Factories loop from arrangements => comment breaks ArrangeFullPortionCommandSpec
    # after(:build) do |op|
    #   op.arrangement = FactoryBot.build(
    #     :future_arrangement,
    #     order_portion: op,
    #     order_id: op.order_id,
    #     order_branch_id: op.order_branch_id,
    #     order_case_id: op.order_case_id
    #   )
    # end
  end

  factory :past_order_portion, parent: :order_portion do
    case_started_at{"2020-01-01 08:00:00".in_time_zone}
    case_ended_at{"2020-01-01 13:00:00".in_time_zone}

    trait :multi_day do
      case_started_at{"2020-01-01 22:00:00".in_time_zone}
      case_ended_at{"2020-01-02 03:00:00".in_time_zone}
    end
  end

  factory :future_order_portion, parent: :order_portion do
    case_started_at{ServerTime.now + 3.days}
    case_ended_at{ServerTime.now + 3.days + 5.hours}
  end

  factory :arranged_portion, parent: :order_portion, traits: [:arranged] do
  end
end
