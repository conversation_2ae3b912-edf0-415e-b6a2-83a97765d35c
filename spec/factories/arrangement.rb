FactoryBot.define do
  factory :arrangement do
    order{FactoryBot.build_stubbed(:order)}
    order_branch{FactoryBot.build_stubbed(:order_branch)}
    order_case{FactoryBot.build_stubbed(:order_case)}
    order_portion{FactoryBot.build_stubbed(:order_portion)}
    working_started_at{Faker::Time.forward}
    working_ended_at{working_started_at + 5.hours}
    break_time{0}
    order_segment_id{1}
    approved_by_admin_id{1}
    current_location_type{"normal_location"}

    after(:build) do |arr|
      arr.arrange_payment = build(:arrange_payment, arrangement: arr)
      arr.arrange_billing = build(:arrange_billing, arrangement: arr)
    end

    trait :with_order do
      after(:build) do |arr|
        o = create(:order, overall_started_at: arr.working_started_at, overall_ended_at: arr.working_ended_at)
        ob = create(:order_branch, order_id: o.id)
        oc = create(
          :order_case, order_id: o.id, order_branch_id: ob.id,
          case_started_at: arr.working_started_at,
          case_ended_at: arr.working_ended_at
        )
        op = create(
          :order_portion, order_id: o.id, order_branch_id: ob.id, order_case_id: oc.id,
          case_started_at: arr.working_started_at,
          case_ended_at: arr.working_ended_at
        )

        arr.order = o
        arr.order_branch = ob
        arr.order_case = oc
        arr.order_portion = op
      end
    end

    trait :arranged do
      display_status_id{"arranged"}
    end

    trait :has_ot_time do
      working_ended_at { working_started_at + 10.hours }
      break_time{60}
    end
  end

  factory :past_arrangement, parent: :arrangement do
    working_started_at{"2020-01-01 08:00:00".in_time_zone}
    working_ended_at{"2020-01-01 13:00:00".in_time_zone}
    break_time{0}
    display_status_id{7}

    trait :multi_day do
      working_started_at{"2020-01-01 22:00:00".in_time_zone}
      working_ended_at{"2020-01-02 03:00:00".in_time_zone}
    end
  end

  factory :future_arrangement, parent: :arrangement do
    working_started_at{ServerTime.now + 3.days}
    working_ended_at{ServerTime.now + 3.days + 5.hours}
  end

  factory :arrangement_with_approved_work_achievement, parent: :arrangement do
    working_started_at{ Faker::Time.backward(days: 30.days) }
    working_ended_at{ working_started_at + 5.hours }

    association :staff, factory: :staff_status_op_confirm

    after(:build) do |arr|
      arr.work_achievement = build(
        :work_achievement, :approved, :create_by_arrangement,
        arrangement: arr,
        working_started_at: arr.working_started_at,
        working_ended_at: arr.working_ended_at,
        staff: arr.staff,
      )
    end
  end
end
