FactoryBot.define do
  factory :location_evaluation do
    location{FactoryBot.build_stubbed(:location)}
    staff{FactoryBot.build_stubbed(:staff)}
    arrangement{FactoryBot.build_stubbed(:arrangement)}
    evaluation { rand(2..4) }
    evaluation_comment { nil }

    trait :with_comment do
      evaluation { [1, 5].sample }
      evaluation_comment { Faker::Lorem.sentence }
    end

    trait :excellent do
      evaluation { 5 }
      evaluation_comment { "Excellent service!" }
    end

    trait :poor do
      evaluation { 1 }
      evaluation_comment { "Needs improvement" }
    end
  end
end
