require "faker"

FactoryBot.define do
  factory :corporation_group do
    pic{build_stubbed(:admin)}
    pic_department{build_stubbed(:department)}
    corporation{build_stubbed(:corporation)}
    name_1{Faker::Company.name}
    creator_id{1}
    updater_id{1}
    billing_postal_code{"060-0810"}
    billing_building{Faker::Address.building_number}
    billing_name_1{Faker::Company.name}
    is_billing_address_differ_current_address{true}
    is_billing_name_differ_corporation_name{true}
    billing_prefecture_id{pic_department.id}
    violation_day{"9999-12-31"}
    chain{FactoryBot.build_stubbed(:chain)}
  end
end
