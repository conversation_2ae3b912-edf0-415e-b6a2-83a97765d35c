require "faker"

FactoryBot.define do
  factory :order do
    corporation{FactoryBot.build_stubbed(:corporation)}
    corporation_group{FactoryBot.build_stubbed(:corporation_group)}
    location{FactoryBot.build_stubbed(:location)}
    organization{FactoryBot.build_stubbed(:organization)}
    location_prefecture_id{1}
    haken_destination_pic_id{1}
    haken_destination_pic_position{"position"}
    haken_destination_pic_tel{"************"}
    mandator_id{1}
    mandator_position{"position"}
    mandator_tel{"************"}
    claim_pic_id{1}
    claim_pic_position{"position"}
    claim_pic_tel{"************"}
    order_pic_id{1}
    order_pic_email{"<EMAIL>"}
    order_pic_tel{"************"}
    is_used_existing_pic{1}
    type_id{1}
    status_id{1}
    current_location_type{"normal_location"}
    overall_started_at{ServerTime.now}
    overall_ended_at{ServerTime.now}
    rejected_at{nil}
    user_approved_at{ServerTime.now}
    admin_approved_at{ServerTime.now}
    pic_department_id{1}
    order_segment_id{"haken"}
    violation_day{"9999-12-31"}
    location_is_store_parking_area_usable{false}

    trait :only_build do
      corporation{FactoryBot.build_stubbed(:corporation, billing_prefecture_id: nil)}
      corporation_group{FactoryBot.build_stubbed(:corporation_group, billing_prefecture_id: nil)}
      location{FactoryBot.build_stubbed(:location, prefecture_id: nil)}
      organization{FactoryBot.build_stubbed(:organization)}
    end
  end

  factory :past_order, parent: :order do
    overall_started_at{"2020-01-01"}
    overall_ended_at{"2020-01-01"}
    user_approved_at{"2020-01-01"}
    admin_approved_at{"2020-01-01"}
  end

  factory :future_order, parent: :order do
    overall_started_at{ServerTime.now + 3.days}
    overall_ended_at{ServerTime.now + 3.days}
  end

  factory :new_order, parent: :future_order do
    order_branches{create_list :new_order_branch, 1}
    # after(:create) do |order|
    #   FactoryBot.create(:new_order_branch, order: order)
    # end
  end
end
