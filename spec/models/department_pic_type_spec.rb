require "rails_helper"

RSpec.describe DepartmentPicType, type: :model do
  describe "Validators" do
    describe "presence" do
      describe "name" do
        it{should validate_presence_of(:name)}
      end
    end
  end

  describe "Associations" do
    it{should have_many(:department_pics)}
  end

  describe "soft delete" do
    it "acts as paranoid" do
      department_pic_type = create(:department_pic_type)
      department_pic_type.destroy
      expect(DepartmentPicType.with_deleted.find(department_pic_type.id)).to eq(department_pic_type)
      expect(DepartmentPicType.find_by(id: department_pic_type.id)).to be_nil
    end
  end
end
