require "rails_helper"

RSpec.describe LocationPic, type: :model do
  describe "Validators" do
    describe "presence" do
      describe "position, tel" do
        before{allow(subject).to receive(:owner_editable).and_return(true)}

        it{should validate_presence_of(:position)}
        it{should validate_presence_of(:tel)}
      end

      describe "name" do
        it{should validate_presence_of(:name)}
      end
    end

    describe "format_rule" do
      let(:location_pic) { build(:location_pic) }

      context 'email' do
        it 'is invalid when email has invalid email format' do
          location_pic.email = 'user@ponos-tech'
          location_pic.valid?

          expect(location_pic.errors.details[:email]).to include(error: :wrong_format)
        end

        it 'is valid when email has valid email format' do
          location_pic.email = '<EMAIL>'
          location_pic.valid?

          expect(location_pic).to be_valid
        end
      end

      context 'tel' do
        it 'is invalid when tel has invalid tel format' do
          location_pic.tel = '01-************'
          location_pic.valid?

          expect(location_pic.errors.details[:tel]).to include(error: :wrong_format)
        end

        it 'is valid when tel has valid tel format' do
          location_pic.tel = '************'
          location_pic.valid?

          expect(location_pic).to be_valid
        end
      end

      context 'fax' do
        it 'is invalid when fax has invalid tel format' do
          location_pic.fax = '01-************'
          location_pic.valid?

          expect(location_pic.errors.details[:fax]).to include(error: :wrong_format)
        end

        it 'is valid when fax has valid tel format' do
          location_pic.fax = '************'
          location_pic.valid?

          expect(location_pic).to be_valid
        end
      end

      context 'cellphone' do
        it 'is invalid when cellphone has invalid tel format' do
          location_pic.cellphone = '01-************'
          location_pic.valid?

          expect(location_pic.errors.details[:cellphone]).to include(error: :wrong_format)
        end

        it 'is valid when cellphone has valid tel format' do
          location_pic.cellphone = '************'
          location_pic.valid?

          expect(location_pic).to be_valid
        end
      end
    end
  end

  describe "Associations" do
    it{should belong_to(:location)}
    it{should belong_to(:location_pic_type).with_foreign_key('pic_type_id')}
  end

  describe "Scopes" do
    context ".by_location_and_type" do
      let(:location_pic_type){ create(:location_pic_type, id: 2) }
      let(:location) { create(:location, id: 1) }
      let!(:location_pic) do
        create(:location_pic, location: location, location_pic_type: location_pic_type)
      end

      before do
        create_list(:location_pic, 2, location: location)
      end

      it "filters by location_id and pic_type_id" do
        result = LocationPic.by_location_and_type(1, 2)
        expect(result).to include(location_pic)
        expect(result.count).to eq(1)
      end
    end

    context ".by_name" do
      it "filters by name" do
        pic = create(:location_pic, name: "Test Name")
        create(:location_pic, name: "Other Name")

        result = LocationPic.by_name("Test Name")
        expect(result).to include(pic)
        expect(result.count).to eq(1)
      end
    end

    context ".by_pic_type_ids" do
      let!(:pic1) { create(:location_pic, location_pic_type: create(:location_pic_type, id: 1)) }
      let!(:pic2) { create(:location_pic, location_pic_type: create(:location_pic_type, id: 2)) }

      before do
        create(:location_pic, location_pic_type: create(:location_pic_type, id: 3))
      end
      it "filters by pic_type_id array" do
        result = LocationPic.by_pic_type_ids([1, 2])
        expect(result).to include(pic1, pic2)
        expect(result.count).to eq(2)
      end
    end
  end

  describe "Class methods" do
    let(:location) { create(:location, id: 1) }

    before do
      create_list(:location_pic, 3, location: location, location_pic_type: create(:location_pic_type, id: 1))
    end

    context ".valid_step_1_location_ids" do
      it "returns location_ids with complete pic data" do
        result = LocationPic.valid_step_1_location_ids
        expect(result).to include(1)
      end
    end

    context ".group_pic_data" do
      it "groups pic data by type" do
        result = LocationPic.group_pic_data(location.id)
        expect(result).to have_key(:haken_destination)
        expect(result[:haken_destination]).to be_an(Array)
      end
    end
  end

  describe "soft delete" do
    it "acts as paranoid" do
      location_pic = create(:location_pic)
      location_pic.destroy
      expect(LocationPic.with_deleted.find(location_pic.id)).to eq(location_pic)
      expect(LocationPic.find_by(id: location_pic.id)).to be_nil
    end
  end

  describe "constants" do
    it "has correct PIC_TYPES" do
      expect(LocationPic::PIC_TYPES).to eq(%i(haken_destination claim mandator))
    end

    it "has correct PIC_TYPE_IDS" do
      expect(LocationPic::PIC_TYPE_IDS).to eq(%w(1 2 3))
    end

    it "has correct PIC_ATTRS" do
      expect(LocationPic::PIC_ATTRS).to eq(%i(id name position tel))
    end

    it "has correct USER_ATTRS" do
      expect(LocationPic::USER_ATTRS).to eq(%i(id name email tel))
    end
  end
end
