require "rails_helper"

RSpec.describe DepartmentPic, type: :model do
  describe "Validators" do
    context "length" do
      it{should validate_length_of(:section).is_at_most(50)}
      it{should validate_length_of(:position).is_at_most(50)}
      it{should validate_length_of(:name).is_at_most(50)}
    end

    context 'valid_date' do
      let(:department_pic) { build(:department_pic) }

      it 'validates valid assigned_at' do
        department_pic.assigned_at = "2025-01-01"
        department_pic.resigned_at = "2025-01-02"
        expect(department_pic).to be_valid
      end

      it 'validates invalid assigned_at when date format is invalid' do
        department_pic.assigned_at = "invalid_date"
        expect(department_pic).not_to be_valid
        expect(department_pic.errors.details[:assigned_at]).to include(error: :invalid_date_format)
      end

      it 'validates resigned_at when date format is invalid' do
        department_pic.resigned_at = "invalid_date"
        expect(department_pic).not_to be_valid
        expect(department_pic.errors.details[:resigned_at]).to include(error: :invalid_date_format)
      end
    end
  end

  describe "Associations" do
    it{should belong_to(:department)}
    it{should belong_to(:department_pic_type)}
  end

  describe "soft delete" do
    it "acts as paranoid" do
      department_pic = create(:department_pic)
      department_pic.destroy
      expect(DepartmentPic.with_deleted.find(department_pic.id)).to eq(department_pic)
      expect(DepartmentPic.find_by(id: department_pic.id)).to be_nil
    end
  end

  describe "constants" do
    it "has correct MAX_LENGTH_50" do
      expect(DepartmentPic::MAX_LENGTH_50).to eq(50)
    end
  end

  describe "strip_attributes" do
    it "strips section, position, name before validation" do
      department_pic = build(:department_pic, section: "  section  ", position: "  position  ", name: "  name  ")
      department_pic.valid?
      expect(department_pic.section).to eq("section")
      expect(department_pic.position).to eq("position")
      expect(department_pic.name).to eq("name")
    end
  end
end
