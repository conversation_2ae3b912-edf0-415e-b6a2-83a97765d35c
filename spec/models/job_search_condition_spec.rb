require "rails_helper"

RSpec.describe JobSearchCondition, type: :model do
  let(:job_search_condition) { build(:job_search_condition) }

  describe "Validators" do
    describe "presence" do
      it { should validate_presence_of(:staff_id) }
      it { should validate_presence_of(:conditions) }
    end
  end

  describe "Associations" do
    it { should belong_to(:staff) }
  end

  describe "soft delete" do
    it "acts as paranoid" do
      condition = create(:job_search_condition)
      condition.destroy
      expect(JobSearchCondition.with_deleted.find(condition.id)).to eq(condition)
      expect(JobSearchCondition.find_by(id: condition.id)).to be_nil
    end
  end

  describe "#from_date" do
    let(:start_date) { "2023-10-15 09:00:00" }
    let(:conditions_json) do
      {
        condition: {
          start_date_from: start_date
        }
      }.to_json
    end
    let(:job_search_condition) { create(:job_search_condition, conditions: conditions_json) }

    before do
      allow(ServerTime).to receive(:name).and_return("Asia/Tokyo")
    end

    it "parses and returns the start_date_from as date" do
      expected_date = Time.zone.parse(start_date).to_date
      expect(job_search_condition.from_date).to eq(expected_date)
    end

    it "handles timezone conversion correctly" do
      expect(job_search_condition.from_date).to be_a(Date)
    end

    context "when conditions JSON is malformed" do
      let(:job_search_condition) { create(:job_search_condition, conditions: "invalid json") }

      it "raises JSON parse error" do
        expect { job_search_condition.from_date }.to raise_error(JSON::ParserError)
      end
    end

    context "when start_date_from is missing" do
      let(:conditions_json) do
        {
          condition: {}
        }.to_json
      end

      it "raises NoMethodError" do
        expect { job_search_condition.from_date }.to raise_error(NoMethodError)
      end
    end
  end

  describe "JSON conditions handling" do
    let(:conditions_hash) do
      {
        condition: {
          start_date_from: ServerTime.now.to_s,
          location_id: 123,
          keyword: "test job"
        }
      }
    end
    let(:job_search_condition) { create(:job_search_condition, conditions: conditions_hash.to_json) }

    it "stores conditions as JSON string" do
      expect(job_search_condition.conditions).to be_a(String)
      expect(JSON.parse(job_search_condition.conditions)).to eq(conditions_hash.as_json)
    end
  end
end
