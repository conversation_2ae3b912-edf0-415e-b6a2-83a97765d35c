require "rails_helper"

RSpec.describe Order, type: :model do
  let(:order) { FactoryBot.build(:order, :only_build) }

  describe "associations" do
    it{should belong_to(:organization)}

    it{should belong_to(:location)}

    it{should belong_to(:corporation_group)}

    it{should belong_to(:corporation)}

    it{should belong_to(:business).optional}

    it{should belong_to(:user_order_pic).class_name('User').with_foreign_key('order_pic_id').optional}

    it{should belong_to(:pic_department).class_name('Department').with_foreign_key('pic_department_id').optional}

    it{should have_many(:order_branches)}

    it{should have_many(:order_cases)}

    it{should have_many(:order_portions)}

    it{should have_many(:staff_apply_order_cases)}

    it{should have_many(:arrangements)}

    it{should have_many(:work_achievements).through(:arrangements)}

    it{should have_many(:arrange_payments).through(:arrangements)}

    it{should have_many(:arrange_billings).through(:arrangements)}

    it{should have_many(:migration_arrangement_histories).through(:arrangements)}

    it{should have_many(:staff_complaints)}
  end

  describe 'validations' do
    context 'presence' do
      it { should validate_presence_of(:corporation_id) }

      it { should validate_presence_of(:corporation_group_id) }

      it { should validate_presence_of(:organization_id) }

      it { should validate_presence_of(:location_id) }

      it { should validate_presence_of(:haken_destination_pic_id) }

      it { should validate_presence_of(:haken_destination_pic_position) }

      it { should validate_presence_of(:haken_destination_pic_tel) }

      it { should validate_presence_of(:mandator_id) }

      it { should validate_presence_of(:mandator_position) }

      it { should validate_presence_of(:mandator_tel) }

      it { should validate_presence_of(:claim_pic_id) }

      it { should validate_presence_of(:claim_pic_position) }

      it { should validate_presence_of(:claim_pic_tel) }

      it { should validate_presence_of(:order_pic_id) }

      it { should validate_presence_of(:order_pic_email) }

      it { should validate_presence_of(:order_pic_tel) }

      it { should validate_presence_of(:overall_started_at) }

      it { should validate_presence_of(:overall_ended_at) }
    end

    context 'length' do
      it {
        should validate_length_of(:definition_id)
          .is_at_most(Settings.maxlength.corporation.order.definition_id)
      }

      it { should validate_length_of(:billing_tel).is_at_most(Settings.maxlength.common.tel) }

      it { should validate_length_of(:note).is_at_most(Settings.maxlength.order.note) }
    end

    context 'is_used_existing_pic' do
      it 'is valid when is_used_existing_pic is true' do
        order.is_used_existing_pic = true
        order.valid?

        expect(order).to be_valid
      end

      it 'is valid when is_used_existing_pic is false' do
        order.is_used_existing_pic = false
        order.valid?

        expect(order).to be_valid
      end

      it 'is invalid when is_used_existing_pic is nil' do
        order.is_used_existing_pic = nil
        order.valid?

        expect(order.errors[:is_used_existing_pic]).to be_present
      end
    end

    context 'format rule tel' do
      %w(haken_destination_pic_tel mandator_tel claim_pic_tel order_pic_tel billing_tel).each do |method|
        it "is invalid when #{method} has invalid tel format" do
          order[method] = '01-************'
          order.valid?

          expect(order.errors[method]).to be_present
        end

        it "is valid when #{method} has valid tel format" do
          order[method] = '************'
          order.valid?

          expect(order).to be_valid
        end
      end
    end

    context 'format rule email' do
      it 'is invalid when order_pic_email has invalid email format' do
        order.order_pic_email = 'user@ponos-tech'
        order.valid?

        expect(order.errors[:order_pic_email]).to be_present
      end

      it 'is valid when order_pic_email has valid email format' do
        order.order_pic_email = '<EMAIL>'
        order.valid?

        expect(order).to be_valid
      end
    end
  end

  describe 'before_validation' do
    let(:order) { FactoryBot.build(:order, :only_build) }

    it 'formats note' do
      order.note = "test\r\ntest"
      order.valid?

      expect(order.note).to eq("test\ntest")
    end
  end

  describe 'enums' do
    it {
      should define_enum_for(:status_id)
        .with_values(draft: 1, waiting_approved: 2, op_checking: 3, confirmed: 4, deny: 5)
    }

    it {
      should define_enum_for(:type_id)
        .with_values(individual: 1, batch: 2, non_recurring: 3, recurring: 4, template: 5)
    }

    it {
      should define_enum_for(:order_segment_id)
        .with_values(haken: 1, training: 2, new_pos_training: 3, rank_up_training: 4,
          career_up_training: 5, regular_order: 6, contract: 7)
    }
  end

  describe 'constants' do
    it 'has correct MAX_ORDER_PORTION' do
      expect(Order::MAX_ORDER_PORTION).to eq(100)
    end

    it 'has correct STATUSES' do
      expected_statuses = {
        draft: "draft",
        waiting_approved: "waiting_approved",
        op_checking: "op_checking",
        confirmed: "confirmed"
      }
      expect(Order::STATUSES).to eq(expected_statuses)
    end
  end

  describe 'soft delete' do
    it 'acts as paranoid' do
      order = create(:order)
      order.destroy
      expect(Order.with_deleted.find(order.id)).to eq(order)
      expect(Order.find_by(id: order.id)).to be_nil
    end
  end

  describe 'nested attributes' do
    it 'accepts nested attributes for order_branches' do
      order_attributes = {
        order_branches_attributes: [
          {"is_except_holiday"=>nil,
            "is_special_offer"=>false,
            "is_time_changable"=>true,
            "required_end_time"=>nil,
            "required_start_time"=>nil,
            "rest1_editable"=>nil},
          { name: 'Branch 2', _destroy: true }
        ]
      }
      expect { order.update(order_attributes) }.not_to raise_error
    end
  end

  describe 'delegates' do
    context 'organization' do
      let(:organization) { FactoryBot.build(:organization) }
      let(:order) { FactoryBot.build(:order, :only_build, organization: organization) }

      it 'delegates to organization_full_name' do
        expect(order.organization_full_name).to eq(organization.full_name)
      end

      it 'delegates to organization_position_name' do
        expect(order.organization_position_name).to eq(organization.position_name)
      end

      it 'raise NoMethodError when delegate to organization in nil' do
        order = described_class.new

        expect { order.organization_full_name }.to raise_error(NoMethodError)
      end
    end

    context 'location' do
      let(:location) { FactoryBot.build(:location) }
      let(:order) { FactoryBot.build(:order, :only_build, location: location) }

      it 'delegates to location_name' do
        expect(order.location_name).to eq(location.name)
      end

      it 'delegates to location_full_address' do
        expect(order.location_full_address).to eq(location.full_address)
      end

      it 'delegates to location_code' do
        expect(order.location_code).to eq(location.code)
      end

      it 'delegates to location_station_1' do
        expect(order.location_station_1).to eq(location.station_1)
      end

      it 'delegates to location_station_2' do
        expect(order.location_station_2).to eq(location.station_2)
      end

      it 'delegates to location_station_3' do
        expect(order.location_station_3).to eq(location.station_3)
      end

      it 'delegates to location_address' do
        expect(order.location_address).to eq(location.address)
      end

      it 'delegates to location_station_4' do
        expect(order.location_station_4).to eq(location.station_4)
      end

      it 'delegates to location_station_5' do
        expect(order.location_station_5).to eq(location.station_5)
      end

      it 'delegates to location_prefecture_name' do
        expect(order.location_prefecture_name).to eq(location.prefecture_name)
      end

      it 'delegates to location_note' do
        expect(order.location_note).to eq(location.note)
      end

      it 'delegates to location_short_name' do
        expect(order.location_short_name).to eq(location.short_name)
      end

      it 'delegates to location_station_1_info' do
        expect(order.location_station_1_info).to eq(location.station_1_info)
      end

      it 'delegates to location_station_2_info' do
        expect(order.location_station_2_info).to eq(location.station_2_info)
      end

      it 'delegates to location_station_3_info' do
        expect(order.location_station_3_info).to eq(location.station_3_info)
      end

      it 'delegates to location_station_4_info' do
        expect(order.location_station_4_info).to eq(location.station_4_info)
      end

      it 'delegates to location_station_5_info' do
        expect(order.location_station_5_info).to eq(location.station_5_info)
      end

      it 'delegates to location_stations_1_name' do
        expect(order.location_stations_1_name).to eq(location.stations_1_name)
      end

      it 'delegates to location_stations_2_name' do
        expect(order.location_stations_2_name).to eq(location.stations_2_name)
      end

      it 'delegates to location_stations_3_name' do
        expect(order.location_stations_3_name).to eq(location.stations_3_name)
      end

      it 'delegates to location_stations_4_name' do
        expect(order.location_stations_4_name).to eq(location.stations_4_name)
      end

      it 'delegates to location_stations_5_name' do
        expect(order.location_stations_5_name).to eq(location.stations_5_name)
      end

      it 'delegates to location_latitude' do
        expect(order.location_latitude).to eq(location.latitude)
      end

      it 'delegates to location_longitude' do
        expect(order.location_longitude).to eq(location.longitude)
      end

      it 'delegates to location_tel' do
        expect(order.location_tel).to eq(location.tel)
      end

      it 'delegates to location_stations_1_station_name' do
        expect(order.location_stations_1_station_name).to eq(location.stations_1_station_name)
      end

      it 'delegates to location_pos_type_id' do
        expect(order.location_pos_type_id).to eq(location.pos_type_id)
      end

      it 'delegates to location_email' do
        expect(order.location_email).to eq(location.email)
      end

      it 'delegates to location_only_name' do
        expect(order.location_only_name).to eq(location.only_name)
      end

      it 'delegates to location_prefecture_name' do
        expect(order.location_prefecture_name).to eq(location.prefecture_name)
      end

      it 'delegates to location_location_type' do
        expect(order.location_location_type).to eq(location.location_type)
      end

      it 'delegates to location_caution_to_staff_mail' do
        expect(order.location_caution_to_staff_mail).to eq(location.caution_to_staff_mail)
      end

      it 'delegates to location_get_type' do
        expect(order.location_get_type).to eq(location.get_type)
      end

      it 'delegates to location_thumbnail_path' do
        expect(order.location_thumbnail_path).to eq(location.thumbnail_path)
      end

      it 'delegates to location_thumbnail_background_path' do
        expect(order.location_thumbnail_background_path).to eq(location.thumbnail_background_path)
      end

      it 'delegates to location_logo' do
        expect(order.location_logo).to eq(location.logo)
      end

      it 'delegates to location_job_content' do
        expect(order.location_job_content).to eq(location.job_content)
      end

      it 'delegates to location_personal_things' do
        expect(order.location_personal_things).to eq(location.personal_things)
      end

      it 'delegates to location_clothes' do
        expect(order.location_clothes).to eq(location.clothes)
      end

      it 'delegates to location_special_note' do
        expect(order.location_special_note).to eq(location.special_note)
      end

      it 'delegates to location_job_category_name' do
        expect(order.location_job_category_name).to eq(location.job_category_name)
      end

      it 'delegates to location_job_categories_text' do
        expect(order.location_job_categories_text).to eq(location.job_categories_text)
      end

      it 'delegates to location_is_export_timesheet' do
        expect(order.location_is_export_timesheet).to eq(location.is_export_timesheet)
      end

      it 'delegates to location_station_1_short_info' do
        expect(order.location_station_1_short_info).to eq(location.station_1_short_info)
      end

      it 'delegates to location_camelized_type' do
        expect(order.location_camelized_type).to eq(location.camelized_type)
      end

      it 'returns nil when location is nil' do
        order = described_class.new
        expect(order.location_name).to be_nil
      end
    end

    context 'corporation_group' do
      let(:corporation_group) { FactoryBot.build(:corporation_group) }
      let(:order) { FactoryBot.build(:order, :only_build, corporation_group: corporation_group) }

      it 'delegates to corporation_group_violation_day' do
        expect(order.corporation_group_violation_day).to eq(corporation_group.violation_day)
      end

      it 'delegates to corporation_group_pic_department_id' do
        expect(order.corporation_group_pic_department_id).to eq(corporation_group.pic_department_id)
      end

      it 'delegates to corporation_group_haken_acceptance_started_at' do
        expect(order.corporation_group_haken_acceptance_started_at).to eq(corporation_group.haken_acceptance_started_at)
      end

      it 'delegates to corporation_group_full_name' do
        expect(order.corporation_group_full_name).to eq(corporation_group.full_name)
      end

      it 'raise NoMethodError when delegate to corporation_group in nil' do
        order = described_class.new
        expect { order.corporation_group_full_name }.to raise_error(NoMethodError)
      end
    end

    context 'corporation' do
      let(:corporation) { FactoryBot.build(:corporation) }
      let(:order) { FactoryBot.build(:order, :only_build, corporation: corporation) }

      it 'delegates to corporation_full_name' do
        expect(order.corporation_full_name).to eq(corporation.full_name)
      end

      it 'delegates to corporation_address' do
        expect(order.corporation_address).to eq(corporation.address)
      end

      it 'delegates to corporation_representative_name' do
        expect(order.corporation_representative_name).to eq(corporation.representative_name)
      end

      it 'delegates to corporation_tel' do
        expect(order.corporation_tel).to eq(corporation.tel)
      end

      it 'raise NoMethodError when delegate to corporation in nil' do
        order = described_class.new
        expect { order.corporation_full_name }.to raise_error(NoMethodError)
      end
    end
  end

  describe 'after initialize' do
    it 'calls copied_data' do
      expect_any_instance_of(described_class).to receive(:copied_data).and_call_original

      described_class.new
    end
  end

  describe 'after update' do
    let(:order) { FactoryBot.create(:order, :only_build) }

    it 'calls update_order_cases' do
      expect_any_instance_of(described_class).to receive(:update_order_cases).and_call_original

      order.update(status_id: :confirmed)
    end
  end

  describe 'before save' do
    let(:order) { FactoryBot.build(:order, :only_build) }

    it 'calls init_pic_admin_data' do
      expect_any_instance_of(described_class).to receive(:init_pic_admin_data)

      order.save
    end

    context '#add_current_location_type' do
      let(:location) { FactoryBot.create(:location) }
      let(:order) { FactoryBot.build(:order, :only_build, location: location, status_id: :confirmed) }

      it 'sets current location type to new_location when last order is blank' do
        order.save
        expect(order.current_location_type).to eq('new_location')
      end

      it 'sets current location type to old_location when last order is created more than 30 days ago' do
        FactoryBot.create(:order, :only_build, location: location, status_id: :confirmed, created_at: 31.days.ago)
        order.save
        expect(order.current_location_type).to eq('old_location')
      end

      it 'sets current location type to normal_location when last order is created less than 30 days ago' do
        FactoryBot.create(:order, :only_build, location: location, status_id: :confirmed, created_at: 29.days.ago)
        order.save
        expect(order.current_location_type).to eq('normal_location')
      end

      it 'does not change current location type when order is draft' do
        order.status_id = :draft
        order.current_location_type = :old_location
        order.save
        expect(order.current_location_type).to eq('old_location')
      end

      it 'does not change current location type when order is not new record and status is not changed' do
        order = FactoryBot.create(:order, :only_build, status_id: :confirmed)
        order.update_columns(current_location_type: :old_location)
        order.note = 'test'
        order.save
        expect(order.current_location_type).to eq('old_location')
      end
    end
  end

  describe 'scopes' do
    context '.updated_by_user' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.updated_by_user(1)).to be_an(ActiveRecord::Relation)
      end
    end

    context '.by_corporation_groups' do
      it 'returns ActiveRecord::Relation when corporation_group_ids is an array' do
        expect(described_class.by_corporation_groups([1, 2])).to be_an(ActiveRecord::Relation)
      end

      it 'returns ActiveRecord::Relation when corporation_group_ids is value' do
        expect(described_class.by_corporation_groups(1)).to be_an(ActiveRecord::Relation)
      end
    end

    context '.by_locations' do
      it 'returns ActiveRecord::Relation when location_ids is an array' do
        expect(described_class.by_locations([1, 2])).to be_an(ActiveRecord::Relation)
      end

      it 'returns ActiveRecord::Relation when location_ids is value' do
        expect(described_class.by_locations(1)).to be_an(ActiveRecord::Relation)
      end
    end

    context '.by_corporations' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.by_corporations([1, 2])).to be_an(ActiveRecord::Relation)
      end
    end

    context '.by_occupations' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.by_occupations([1, 2])).to be_an(ActiveRecord::Relation)
      end
    end

    context '.by_organizations' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.by_organizations([1, 2])).to be_an(ActiveRecord::Relation)
      end
    end

    context '.by_businesses' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.by_businesses([1, 2])).to be_an(ActiveRecord::Relation)
      end
    end

    context '.by_order_segments' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.by_order_segments([1, 2])).to be_an(ActiveRecord::Relation)
      end
    end

    context '.by_statuses' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.by_statuses([1, 2])).to be_an(ActiveRecord::Relation)
      end
    end

    context '.exist_order_less_than_closed_at' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.exist_order_less_than_closed_at(Time.current)).to be_an(ActiveRecord::Relation)
      end
    end

    context '.created_in_range' do
      it 'returns ActiveRecord::Relation' do
        expect(described_class.created_in_range(Time.current, Time.current + 1.day))
          .to be_an(ActiveRecord::Relation)
      end
    end
  end

  describe 'instance methods' do
    let(:order) { FactoryBot.build(:order, :only_build) }

    context '#total_order_portions' do
      let(:order) do
        FactoryBot.create(:order, :only_build,
          order_portions_count: 2,
          estimate_total_portion: 4
        )
      end

      it 'returns order_portions_count value when status_id is confirmed' do
        order.status_id = :confirmed
        expect(order.total_order_portions).to eq(2)
      end

      it 'returns estimate_total_portion value when status_id is not confirmed' do
        order.status_id = [:draft, :waiting_approved, :op_checking, :deny].sample
        expect(order.total_order_portions).to eq(4)
      end
    end

    context '#update_total_order_portions' do
      let(:order) { FactoryBot.create(:order, :only_build, order_portions_count: 1) }

      it 'updates column order_portions_count by valid order portions' do
        FactoryBot.create_list(:order_portion, 3, :not_arrange, order_id: order.id)

        order.update_total_order_portions

        expect(order.order_portions_count).to eq(3)
      end
    end

    context '#copied_data' do
      let(:order) { FactoryBot.create(:order, :only_build) }

      it 'early returns when order is not new record' do
        expect(order.copied_data).to be_nil
      end

      it 'early returns when order is migrated' do
        order = described_class.new
        order.is_migrated = true
        expect(order.copied_data).to be_nil
      end

      it 'calls #change_params_data' do
        order = described_class.new
        order.is_migrated = false

        expect(order).to receive(:change_params_data)

        order.copied_data
      end
    end

    context '#should_create_owner_request_notification?' do
      let(:order) { FactoryBot.build(:order, :only_build, status_id: :waiting_approved) }

      it 'returns true when previous_status is draft and current status is waiting_approved' do
        expect(order.should_create_owner_request_notification?('draft')).to eq(true)
      end

      it 'returns false when previous_status is not draft' do
        expect(order.should_create_owner_request_notification?('confirmed')).to eq(false)
      end

      it 'returns false when current status is not waiting_approved' do
        order.status_id = :confirmed
        expect(order.should_create_owner_request_notification?('draft')).to eq(false)
      end
    end

    context '#should_create_owner_approve_notification?' do
      let(:order) { FactoryBot.build(:order, :only_build, status_id: :confirmed) }

      it 'returns true when previous_status is waiting_approved and current status is confirmed' do
        expect(order.should_create_owner_approve_notification?('waiting_approved')).to eq(true)
      end

      it 'returns false when previous_status is not waiting_approved' do
        expect(order.should_create_owner_approve_notification?('confirmed')).to eq(false)
      end

      it 'returns false when current status is not confirmed' do
        order.status_id = :op_checking
        expect(order.should_create_owner_approve_notification?('waiting_approved')).to eq(false)
      end
    end

    context '#op_center_should_create_notification?' do
      let(:order) { FactoryBot.build(:order, :only_build, status_id: :confirmed) }

      it 'returns true when previous_status is waiting_approved or op_checking and current status is confirmed' do
        expect(order.op_center_should_create_notification?('waiting_approved')).to eq(true)
        expect(order.op_center_should_create_notification?('op_checking')).to eq(true)
      end

      it 'returns false when previous_status is not waiting_approved or op_checking' do
        expect(order.op_center_should_create_notification?('confirmed')).to eq(false)
      end

      it 'returns false when current status is not confirmed' do
        order.status_id = :waiting_approved
        expect(order.op_center_should_create_notification?('op_checking')).to eq(false)
      end
    end

    context '#should_delete_owner_notification?' do
      let(:order) { FactoryBot.build(:order, :only_build, status_id: [:confirmed, :draft].sample) }

      it 'returns true when previous_status is waiting_approved and current status is confirmed or draft' do
        expect(order.should_delete_owner_notification?('waiting_approved')).to eq(true)
        expect(order.should_delete_owner_notification?('waiting_approved')).to eq(true)
      end

      it 'returns false when previous_status is not waiting_approved' do
        expect(order.should_delete_owner_notification?('confirmed')).to eq(false)
      end

      it 'returns false when current status is not confirmed or draft' do
        order.status_id = :op_checking
        expect(order.should_delete_owner_notification?('waiting_approved')).to eq(false)
      end
    end

    context '#should_authority_owner_create_notification?' do
      let(:order) { FactoryBot.build(:order, :only_build, status_id: :confirmed) }
      let(:user) { FactoryBot.build(:user) }

      before do
        allow(order).to receive(:user_confirm_order?)
          .with(user)
          .and_return(true)
      end

      it 'returns true when order is confirmed and #user_confirm_order? is true' do
        expect(order.should_authority_owner_create_notification?(user)).to eq(true)
      end

      it 'returns false when order is not confirmed' do
        order.status_id = :draft
        expect(order.should_authority_owner_create_notification?(user)).to eq(false)
      end

      it 'returns false when #user_confirm_order? is false' do
        allow(order).to receive(:user_confirm_order?)
          .with(user)
          .and_return(false)

        expect(order.should_authority_owner_create_notification?(user)).to eq(false)
      end
    end

    context '#should_owner_update_order_create_notification?' do
      let(:user) { FactoryBot.build(:user) }

      before do
        allow(order).to receive(:user_confirm_order?)
          .with(user)
          .and_return(true)
      end

      it 'returns true when order is confirmed and #user_confirm_order? is true' do
        order.status_id = :confirmed
        expect(order.should_owner_update_order_create_notification?(user)).to eq(true)
      end

      it 'returns true when order is draft and #user_confirm_order? is true' do
        order.status_id = :draft
        expect(order.should_owner_update_order_create_notification?(user)).to eq(true)
      end

      it 'returns false when order is not confirmed or draft' do
        order.status_id = :op_checking
        expect(order.should_owner_update_order_create_notification?(user)).to eq(false)
      end

      it 'returns false when #user_confirm_order? is false' do
        allow(order).to receive(:user_confirm_order?)
          .with(user)
          .and_return(false)

        expect(order.should_owner_update_order_create_notification?(user)).to eq(false)
      end
    end

    context '#user_confirm_order?' do
      let(:user) { FactoryBot.build(:user) }

      before do
        allow(order.location).to receive(:is_approval_required)
          .and_return(true)
      end

      it 'returns true when user is not normal' do
        user.role_id = :owner
        expect(order.user_confirm_order?(user)).to eq(true)
      end

      it 'returns true when user is normal and location is_approval_required is false' do
        allow(order.location).to receive(:is_approval_required).and_return(false)
        user.role_id = :normal

        expect(order.user_confirm_order?(user)).to eq(true)
      end

      it 'returns false when user is normal and order location is_approval_required is true' do
        user.role_id = :normal
        expect(order.user_confirm_order?(user)).to eq(false)
      end
    end

    context '#init_pic_admin_data' do
      before(:context) do
        FactoryBot.create(:department_pic_type, id: Settings.order.pic_types.haken)
        FactoryBot.create(:department_pic_type, id: Settings.order.pic_types.claim_name)
      end

      let(:department) { FactoryBot.create(:department) }

      before do
        FactoryBot.create(:department_pic,
          department_pic_type_id: Settings.order.pic_types.haken,
          department: department,
          name: "Haken name",
          position: "Haken position"
        )

        FactoryBot.create(:department_pic,
          department_pic_type_id: Settings.order.pic_types.claim_name,
          department: department,
          name: "Claim name",
          position: "Claim position"
        )
      end

      it 'returns when order is migrated' do
        order.is_migrated = true
        expect(order.init_pic_admin_data).to be_nil
      end

      it 'returns when pic_department is nil' do
        order.pic_department = nil
        expect(order.init_pic_admin_data).to be_nil
      end

      it 'returns when department_pics is blank' do
        order.pic_department = department
        department.department_pics.destroy_all

        expect(order.init_pic_admin_data).to be_nil
      end

      it 'assigns haken_source_pic_name and haken_source_pic_position' do
        order.pic_department = department
        order.init_pic_admin_data

        expect(order.haken_source_pic_name).to eq("Haken name")
        expect(order.haken_source_pic_position).to eq("Haken position")
      end

      it 'assigns claim_process_pic_name and claim_process_pic_position' do
        order.pic_department = department
        order.init_pic_admin_data

        expect(order.claim_process_pic_name).to eq("Claim name")
        expect(order.claim_process_pic_position).to eq("Claim position")
      end

      it 'does not assign when haken pic type is not matched' do
        order.pic_department = department

        department.department_pics.find_by(department_pic_type_id: Settings.order.pic_types.haken).destroy
        order.init_pic_admin_data

        expect(order.haken_source_pic_name).to be_nil
        expect(order.haken_source_pic_position).to be_nil
      end

      it 'does not assign when claim pic type is not matched' do
        order.pic_department = department
        department.department_pics.find_by(department_pic_type_id: Settings.order.pic_types.claim_name).destroy
        order.init_pic_admin_data

        expect(order.claim_process_pic_name).to be_nil
        expect(order.claim_process_pic_position).to be_nil
      end
    end

    context '#is_from_lawson?' do
      let(:outside_lawson_corporation) { FactoryBot.build(:corporation, is_lawson: false) }
      let(:lawson_corporation) { FactoryBot.build(:corporation, is_lawson: true) }

      it 'returns true when corporation is lawson' do
        order.corporation = lawson_corporation

        expect(order.is_from_lawson?).to eq(true)
      end

      it 'returns false when corporation is not lawson' do
        order.corporation = outside_lawson_corporation

        expect(order.is_from_lawson?).to eq(false)
      end
    end

    context '#is_haken_type_labor?' do
      let(:labor_corporation) { FactoryBot.build(:corporation, haken_type: :labor) }
      let(:equality_corporation) { FactoryBot.build(:corporation, haken_type: :equality) }

      it 'returns true when corporation is labor' do
        order.corporation = labor_corporation
        expect(order.is_haken_type_labor?).to eq(true)
      end

      it 'returns false when corporation is equality' do
        order.corporation = equality_corporation
        expect(order.is_haken_type_labor?).to eq(false)
      end
    end

    context '#created_user' do
      let(:user) { FactoryBot.create(:user) }

      it 'returns user when created_user_id is present' do
        order.created_user_id = user.id
        expect(order.created_user).to eq(user)
      end

      it 'returns nil when created_user_id is not present' do
        order.created_user_id = nil
        expect(order.created_user).to be_nil
      end
    end

    context '#calculate_overall_attributes' do
      let(:order) { FactoryBot.build(:order, :only_build) }
      let(:start_time) { Time.zone.parse("2025-01-01 08:00") }
      let(:order_branches) do
        [
          build(:order_branch, order: order, started_at: start_time - 1.hour, ended_at: start_time + 4.hours),
          build(:order_branch, order: order, started_at: nil, ended_at: start_time + 6.hours),
          build(:order_branch, order: order, started_at: start_time - 2.hours, ended_at: nil),
          build(:order_branch, order: order, started_at: start_time, ended_at: start_time + 5.hours)
        ]
      end

      let(:order_branch_marked_for_destruction) do
        build(:order_branch, order: order, started_at: start_time - 2.hour, ended_at: start_time + 6.hours)
      end

      before do
        order.order_branches = order_branches << order_branch_marked_for_destruction
        order_branch_marked_for_destruction.mark_for_destruction
        order_branches.each do |ob|
          ob.estimate_total_portion = 5
        end

        order.calculate_overall_attributes
      end

      it 'calculates overall_started_at as minimum started_at of valid order branches' do
        expect(order.overall_started_at).to eq(start_time - 1.hours)
      end

      it 'calculates overall_ended_at as maximum ended_at of valid order branches' do
        expect(order.overall_ended_at).to eq(start_time + 5.hours)
      end

      it 'calculates estimate_total_portion as sum of estimate_total_portion of valid order branches' do
        expect(order.estimate_total_portion).to eq(10)
      end
    end

    context '#valid_estimate_total_portion?' do
      let(:order) { FactoryBot.build(:order, :only_build) }

      it 'returns true when estimate total portion is less than or equal to 100' do
        order.estimate_total_portion = 100
        expect(order.valid_estimate_total_portion?).to eq(true)
      end

      it 'returns false when estimate total portion is greater than 100' do
        order.estimate_total_portion = 101
        expect(order.valid_estimate_total_portion?).to eq(false)
      end
    end

    context '#location_parking_area_name' do
      it 'returns parking area name when is_store_parking_area_usable is true' do
        order.location_is_store_parking_area_usable = true
        expect(order.location_parking_area_name).to eq(I18n.t("corporation.order.store_parking.#{true}"))
      end

      it 'returns parking area name when is_store_parking_area_usable is false' do
        order.location_is_store_parking_area_usable = false
        expect(order.location_parking_area_name).to eq(I18n.t("corporation.order.store_parking.#{false}"))
      end
    end

    context '#full_address' do
      let(:prefecture) { FactoryBot.build(:prefecture, name: "Prefecture") }
      let(:location) { FactoryBot.build(:location, prefecture: prefecture) }

      it 'returns full address' do
        order.location = location
        order.location_city = "City"
        order.location_street_number = "Street"
        order.location_building = "Building"

        expect(order.full_address).to eq("PrefectureCityStreetBuilding")
      end
    end

    context '#able_update_by?' do
      it 'returns true when location_id is included in ids' do
        order.location_id = 1
        expect(order.able_update_by?([1, 2])).to eq(true)
      end

      it 'returns false when location_id is not included in ids' do
        order.location_id = 1
        expect(order.able_update_by?([2, 3])).to eq(false)
      end
    end

    context '#to_json_detail' do
      let!(:location) { FactoryBot.create(:location_with_full_data) }
      let(:user) { FactoryBot.build(:user) }

      it 'returns json string' do
        order.location = location
        expect(JSON.parse(order.to_json_detail)).to be_a(Hash)
        expect(JSON.parse(order.to_json_detail(nil, true))).to be_a(Hash)
        expect(JSON.parse(order.to_json_detail(user, false))).to be_a(Hash)
      end
    end

    context '#haken_destination_location_pic_name' do
      let(:location_pic) { FactoryBot.create(:location_pic, name: "Haken name") }
      let(:order) do
        FactoryBot.build(:order, :only_build,
          haken_destination_pic_id: location_pic.id,
          haken_destination_pic_position: "position",
          haken_destination_pic_tel: "************"
        )
      end

      it 'returns name with pic name, position and tel when haken_destination_pic_id is present' do
        expect(order.haken_destination_location_pic_name).to eq("Haken name position ************")
      end

      it 'returns name without pic name when haken_destination_pic_id is not present' do
        order.haken_destination_pic_id = nil
        expect(order.haken_destination_location_pic_name).to eq(" position ************")
      end
    end

    context '#mandator_pic_name' do
      let(:location_pic) { FactoryBot.create(:location_pic, name: "Mandator name") }
      let(:order) do
        FactoryBot.build(:order, :only_build,
          mandator_id: location_pic.id,
          mandator_position: "position",
          mandator_tel: "************"
        )
      end

      it 'returns name with pic name, position and tel when mandator_id is present' do
        expect(order.mandator_pic_name).to eq("Mandator name position ************")
      end

      it 'returns name without pic name when mandator_id is not present' do
        order.mandator_id = nil
        expect(order.mandator_pic_name).to eq(" position ************")
      end
    end

    context '#claim_location_pic_name' do
      let(:location_pic) { FactoryBot.create(:location_pic, name: "Claim name") }
      let(:order) do
        FactoryBot.build(:order, :only_build,
          claim_pic_id: location_pic.id,
          claim_pic_position: "position",
          claim_pic_tel: "************"
        )
      end

      it 'returns name with pic name, position and tel when claim_pic_id is present' do
        expect(order.claim_location_pic_name).to eq("Claim name position ************")
      end

      it 'returns name without pic name when claim_pic_id is not present' do
        order.claim_pic_id = nil
        expect(order.claim_location_pic_name).to eq(" position ************")
      end
    end

    context '#order_pic_name' do
      let(:account) { FactoryBot.create(:account, name: "User name") }
      let(:user) { FactoryBot.create(:user, account: account) }
      let(:order) do
        FactoryBot.build(:order, :only_build,
          order_pic_id: user.id,
          order_pic_email: "<EMAIL>",
          order_pic_tel: "************"
        )
      end

      it 'returns name with user name, email and tel when order_pic_id is present' do
        expect(order.order_pic_name).to eq("<NAME_EMAIL> ************")
      end

      it 'returns name without user name when order_pic_id is not present' do
        order.order_pic_id = nil
        expect(order.order_pic_name).to eq(" <EMAIL> ************")
      end
    end

    context '#order_pic_user_name' do
      let(:account) { FactoryBot.build_stubbed(:account, name: "User name") }
      let(:user) { FactoryBot.build_stubbed(:user, account: account) }
      let(:order) { FactoryBot.build(:order, :only_build, user_order_pic: user) }

      it 'returns user name when order_pic_id is present' do
        expect(order.order_pic_user_name).to eq("User name")
      end

      it 'returns nil when order_pic_id is not present' do
        order.order_pic_id = nil
        expect(order.order_pic_user_name).to be_nil
      end
    end

    context '#haken_period' do
      let(:order) do
        FactoryBot.build(:order, :only_build,
          overall_started_at: "2025-01-01".in_time_zone,
          overall_ended_at: "2025-01-03".in_time_zone
        )
      end

      it 'returns nil when overall_started_at is not present' do
        order.overall_started_at = nil
        expect(order.haken_period).to be_nil
      end

      it 'returns nil when overall_ended_at is not present' do
        order.overall_ended_at = nil
        expect(order.haken_period).to be_nil
      end

      it 'returns haken period when overall_started_at and overall_ended_at are present' do
        allow(DateTimeFormatting).to receive(:full_date_and_day)
          .with(order.overall_started_at)
          .and_return("2025/01/01(Wed)")

        allow(DateTimeFormatting).to receive(:full_date_and_day)
          .with(order.overall_ended_at)
          .and_return("2025/01/03(Fri)")

        expect(order.haken_period).to eq("2025/01/01(Wed)~2025/01/03(Fri)")
      end
    end

    context '#send_mail_change_status' do
      let(:user) { FactoryBot.create(:user) }
      let(:order) { FactoryBot.build(:order, :only_build, created_user_id: user.id) }

      it 'calls SendMailRequestApproveWorker when order_action is waiting_approved' do
        expect(SendMailRequestApproveWorker).to receive(:perform_async)
          .with(order.id, user.id)

        order.send_mail_change_status(false, "waiting_approved", user)
      end

      it 'calls UserMailer with type approve when is_send_order is true and created user is normal' do
        user.update_columns(role_id: :normal)
        expect(UserMailer).to receive(:send_mail_order)
          .with(user, order, :approve)
          .and_return(double(deliver_now: true))

        order.send_mail_change_status(true, nil, user)
      end

      it 'calls UserMailer with type cancel when order_action is repayment and user is not normal' do
        user.update_columns(role_id: :owner)
        expect(UserMailer).to receive(:send_mail_order)
          .with(user, order, :cancel)
          .and_return(double(deliver_now: true))
        order.send_mail_change_status(false, "repayment", user)
      end
    end

    context '#exported_label' do
      it 'returns exported label when is_exported is true' do
        order.is_exported = true
        expect(order.exported_label).to eq(I18n.t("admin.order.order_list_page.export_status.exported"))
      end

      it 'returns not exported label when is_exported is false' do
        order.is_exported = false
        expect(order.exported_label).to eq(I18n.t("admin.order.order_list_page.export_status.not_exported"))
      end
    end

    context '#business_name' do
      let(:location) { FactoryBot.build(:location) }
      let(:order) { FactoryBot.build(:order, :only_build, location: location) }

      it 'returns business name by location job categories text' do
        allow(location).to receive(:job_categories_text).and_return("Business name")
        expect(order.business_name).to eq("Business name")
      end

      it 'returns nil when location is not present' do
        order.location = nil
        expect(order.business_name).to be_nil
      end
    end

    context '#pic_department_name' do
      let(:location) { FactoryBot.build(:location, department: department) }
      let(:order) { FactoryBot.build(:order, :only_build, location: location) }
      let(:department) { FactoryBot.build(:department, name: "Department name") }

      it 'returns department name when location is present' do
        expect(order.pic_department_name).to eq("Department name")
      end

      it 'returns nil when location is not present' do
        order.location = nil
        expect(order.pic_department_name).to be_nil
      end
    end

    context '#real_pic_department_name' do
      let(:department) { FactoryBot.build(:department, name: "Department name") }
      let(:order) { FactoryBot.build(:order, :only_build, pic_department: department) }

      it 'returns department name when pic_department is present' do
        expect(order.real_pic_department_name).to eq("Department name")
      end

      it 'returns nil when pic_department is not present' do
        order.pic_department = nil
        expect(order.real_pic_department_name).to be_nil
      end
    end

    context '#segment_name' do
      let(:order) { FactoryBot.build(:order, :only_build) }

      it 'returns segment name when order_segment_id is present' do
        order.order_segment_id = "haken"

        expect(order.segment_name).to eq(I18n.t("corporation.order.segment_ids.haken"))
      end

      it 'returns empty string when order_segment_id is not present' do
        order.order_segment_id = nil
        expect(order.segment_name).to eq("")
      end
    end

    context '#id_with_leading_zeros' do
      let(:order) { FactoryBot.build(:order, :only_build) }

      it 'returns id with leading zeros when number of digits is less than 4' do
        order.id = 1
        expect(order.id_with_leading_zeros).to eq("0001")
      end

      it 'returns id with leading zeros when id is equal to or greater than 4 digits' do
        order.id = 1001
        expect(order.id_with_leading_zeros).to eq("1001")
      end
    end

    context '#order_created_at' do
      let(:order) { FactoryBot.build(:order, :only_build, created_at: "2025-01-01".in_time_zone) }

      it 'returns nil when order status is draft' do
        order.status_id = "draft"
        expect(order.order_created_at).to be_nil
      end

      it 'returns formatted created at when order status is not draft' do
        order.status_id = "waiting_approved"
        expect(order.order_created_at)
          .to eq(I18n.l("2025-01-01".in_time_zone, format: Settings.date.day_and_date))
      end
    end

    context '#change_params_data' do
      let(:corporation_group) { FactoryBot.create(:corporation_group) }
      let(:organization) { FactoryBot.create(:organization, corporation_group: corporation_group) }
      let(:location) { FactoryBot.create(:location) }
      let!(:location_pic) do
        FactoryBot.create(:location_pic, id: 1,  location: location, name: "Haken name")
        FactoryBot.create(:location_pic, id: 2, location: location, name: "Claim name")
        FactoryBot.create(:location_pic, id: 3, location: location, name: "Mandator name")
      end
      let(:order) do
        described_class.new(
          haken_destination_pic_id: 1,
          claim_pic_id: 2,
          mandator_id: 3
        )
      end

      it 'does not change params when organization_id is not present' do
        order.organization_id = nil
        order.change_params_data
        expect(order.corporation_group_id).to be_nil
        expect(order.corporation_id).to be_nil
        expect(order.violation_day).to be_nil
        expect(order.location_longitude).to be_nil
        expect(order.location_latitude).to be_nil
        expect(order.location_postal_code).to be_nil
        expect(order.location_prefecture_id).to be_nil
        expect(order.location_city).to be_nil
        expect(order.location_street_number).to be_nil
        expect(order.location_building).to be_nil
        expect(order.location_tel).to be_nil
        expect(order.location_fax).to be_nil
        expect(order.location_email).to be_nil
        expect(order.location_is_store_parking_area_usable).to be_nil
        expect(order.haken_destination_pic_name).to be_nil
        expect(order.mandator_name).to be_nil
        expect(order.claim_pic_name).to be_nil
      end

      it 'does not change params when location_id is not present' do
        order.location_id = nil
        order.change_params_data

        expect(order.corporation_group_id).to be_nil
        expect(order.corporation_id).to be_nil
        expect(order.violation_day).to be_nil
        expect(order.location_longitude).to be_nil
        expect(order.location_latitude).to be_nil
        expect(order.location_postal_code).to be_nil
        expect(order.location_prefecture_id).to be_nil
        expect(order.location_city).to be_nil
        expect(order.location_street_number).to be_nil
        expect(order.location_building).to be_nil
        expect(order.location_tel).to be_nil
        expect(order.location_fax).to be_nil
        expect(order.location_email).to be_nil
        expect(order.location_is_store_parking_area_usable).to be_nil
      end

      it 'changes field values when organization_id and location_id are present' do
        order.organization_id = organization.id
        order.location_id = location.id
        order.change_params_data

        expect(order.corporation_group_id).not_to be_nil
        expect(order.corporation_group_id).to eq(organization.corporation_group_id)
        expect(order.corporation_id).not_to be_nil
        expect(order.corporation_id).to eq(organization.corporation_group.corporation_id)
        expect(order.violation_day).not_to be_nil
        expect(order.violation_day).to eq(organization.corporation_group.violation_day)
        expect(order.location_longitude).to eq(location.longitude)
        expect(order.location_latitude).to eq(location.latitude)
        expect(order.location_postal_code).to eq(location.postal_code)
        expect(order.location_prefecture_id).to eq(location.prefecture_id)
        expect(order.location_city).to eq(location.city)
        expect(order.location_street_number).to eq(location.street_number)
        expect(order.location_building).to eq(location.building)
        expect(order.location_tel).to eq(location.tel)
        expect(order.location_fax).to eq(location.fax)
        expect(order.location_email).to eq(location.email)
        expect(order.location_is_store_parking_area_usable).to eq(location.is_store_parking_area_usable)
        expect(order.haken_destination_pic_name).to eq("Haken name")
        expect(order.mandator_name).to eq("Mandator name")
        expect(order.claim_pic_name).to eq("Claim name")
      end
    end

    context '#location_code_format' do
      let(:location) { FactoryBot.build(:location, code: "123456") }
      let(:order) { FactoryBot.build(:order, :only_build) }

      it 'returns location code formatted' do
        order.location = location
        expect(order.location_code_format).to eq("【ダミー】<000000>123456")
      end
    end

    context '#export_location_address' do
      let(:prefecture) { FactoryBot.build(:prefecture, name: "Prefecture") }
      let(:location) { FactoryBot.build(:location, prefecture: prefecture) }
      let(:order) do
        FactoryBot.build(:order, :only_build,
          location: location,
          location_city: "City",
          location_street_number: "Street"
        )
      end

      it 'returns location address formatted' do
        expect(order.export_location_address).to eq("PrefectureCityStreet")
      end
    end

    context '#haken_source_pic_tel' do
      let(:pic_department) { FactoryBot.build(:department, address_tel: "************") }
      let(:coporation_group) { FactoryBot.build(:corporation_group, pic_department: pic_department) }
      let(:order) { FactoryBot.build(:order, :only_build, corporation_group: coporation_group) }

      it 'returns haken source pic tel' do
        expect(order.haken_source_pic_tel).to eq("************")
      end
    end

    context '#set_pic_department_id' do
      let(:department) { FactoryBot.create(:department, id: 1) }
      let(:location) { FactoryBot.create(:location, department: department) }
      let(:order) { FactoryBot.build(:order, :only_build, location: location) }

      it 'sets pic department id' do
        order.set_pic_department_id
        expect(order.pic_department_id).to eq(1)
      end
    end

    context '#recurring_order' do
      let(:order) { FactoryBot.build(:order, :only_build) }

      it 'returns true when order type is recurring' do
        order.type_id = "recurring"
        expect(order.recurring_order).to be_truthy
      end

      it 'returns false when order type is not recurring' do
        order.type_id = "individual"
        expect(order.recurring_order).to be_falsey
      end
    end

    context '#recurring_working_time' do
      let(:order) { FactoryBot.create(:order, :only_build) }

      before do
        FactoryBot.create(:order_branch,
          order: order, is_sunday: true, is_monday: true,
          is_tuesday: true, is_wednesday: true, is_thursday: true,
          is_friday: nil, is_saturday: true
        )

        order.reload
      end

      it 'returns recurring working time' do
        exepected_days = Settings.date.weekdays.values_at(0, 1, 2, 3, 4, 6).join(" ")
        expect(order.recurring_working_time).to eq("(#{exepected_days})")
      end
    end

    context '#overall_working_dates' do
      let(:order) { FactoryBot.build(:order, :only_build) }

      it 'returns overall working dates' do
        order.overall_started_at = "2025-01-01".in_time_zone
        order.overall_ended_at = "2025-01-03".in_time_zone
        expect(order.overall_working_dates).to eq("2025/01/01 ~ 2025/01/03")
      end
    end

    context '#regular_order_timeout_apply?' do
      let(:order) do
        FactoryBot.build(:order, :only_build, order_segment_id: "regular_order",
          last_started_at: ServerTime.now - 1.day
        )
      end

      it 'returns nil when order is not regular' do
        order.order_segment_id = "haken"
        expect(order.regular_order_timeout_apply?).to be_nil
      end

      it 'returns nil when last started at is not present' do
        order.last_started_at = nil
        expect(order.regular_order_timeout_apply?).to be_nil
      end

      it 'returns true when last started at is less than or equal to server time' do
        expect(order.regular_order_timeout_apply?).to be_truthy
      end

      it 'returns false when last started at is greater than server time' do
        order.last_started_at = ServerTime.now + 1.day
        expect(order.regular_order_timeout_apply?).to be_falsey
      end
    end

    context '#is_created_by_owner?' do
      let(:order) { FactoryBot.build(:order, :only_build) }

      it 'returns true when created user id is present' do
        order.created_user_id = 1
        expect(order.is_created_by_owner?).to be_truthy
      end

      it 'returns false when created user id is not present' do
        order.created_user_id = nil
        expect(order.is_created_by_owner?).to be_falsey
      end
    end

    context '#skip_staff_violation?' do
      let(:order) { FactoryBot.build(:order, :only_build) }

      it 'returns true when order is not haken' do
        order.order_segment_id = "training"
        expect(order.skip_staff_violation?).to be_truthy
      end

      it 'returns true when is_fixed_term_project is true' do
        order.is_fixed_term_project = true
        expect(order.skip_staff_violation?).to be_truthy
      end

      it 'returns true when is_limited_day is true' do
        order.is_limited_day = true
        expect(order.skip_staff_violation?).to be_truthy
      end

      it 'returns false when order is haken and is_fixed_term_project and is_limited_day are false' do
        order.order_segment_id = "haken"
        order.is_fixed_term_project = false
        order.is_limited_day = false
        expect(order.skip_staff_violation?).to be_falsey
      end
    end

    context '#used_payment_billing_template?' do
      let(:order) { FactoryBot.create(:order, :only_build)}

      before do
        FactoryBot.create_list(:arrangement, 2, order: order, billing_payment_template_id: nil)
        order.reload
      end

      it 'returns true when arrangement has billing payment template id' do
        order.arrangements.first.update_columns(billing_payment_template_id: 1)

        expect(order.used_payment_billing_template?).to be_truthy
      end

      it 'returns false when all arrangements does not have billing payment template id' do
        order.arrangements.update_all(billing_payment_template_id: nil)

        expect(order.used_payment_billing_template?).to be_falsey
      end
    end

    context '#created_full_date' do
      let(:order) { FactoryBot.build(:order, :only_build, created_at: "2025-01-01".in_time_zone) }

      it 'returns formatted created at' do
        expect(order.created_full_date)
          .to eq(I18n.l("2025-01-01".in_time_zone, format: Settings.date.full_date_time))
      end
    end
  end

  describe 'class methods' do
    context '#status_id_options' do
      let(:corporation_pre_status) {'corporation.order.status_ids'}
      let(:admin_pre_status) {'admin.order.order_list_page.status_ids'}

      it 'returns status id options for corporation' do
        expected_options = [
          {id: 1, key: "draft", name: I18n.t("#{corporation_pre_status}.draft")},
          {id: 2, key: "waiting_approved", name: I18n.t("#{corporation_pre_status}.waiting_approved")},
          {id: 3, key: "op_checking", name: I18n.t("#{corporation_pre_status}.op_checking")},
          {id: 4, key: "confirmed", name: I18n.t("#{corporation_pre_status}.confirmed")},
          {id: 5, key: "deny", name: I18n.t("#{corporation_pre_status}.deny")}
        ]
        expect(Order.status_id_options("corporation")).to eq(expected_options)
      end

      it 'returns status id options for admin' do
        expected_options = [
          {id: 1, key: "draft", name: I18n.t("#{admin_pre_status}.draft")},
          {id: 2, key: "waiting_approved", name: I18n.t("#{admin_pre_status}.waiting_approved")},
          {id: 3, key: "op_checking", name: I18n.t("#{admin_pre_status}.op_checking")},
          {id: 4, key: "confirmed", name: I18n.t("#{admin_pre_status}.confirmed")},
          {id: 5, key: "deny", name: I18n.t("#{admin_pre_status}.deny")}
        ]
        expect(Order.status_id_options("admin")).to eq(expected_options)
      end
    end

    context '#order_segment_options' do
      it 'returns order segment options' do
        expected_options = [
          {id: 1, key: "haken", name: I18n.t("corporation.order.segment_ids.haken")},
          {id: 2, key: "training", name: I18n.t("corporation.order.segment_ids.training")},
          {id: 3, key: "new_pos_training", name: I18n.t("corporation.order.segment_ids.new_pos_training")},
          {id: 4, key: "rank_up_training", name: I18n.t("corporation.order.segment_ids.rank_up_training")},
          {id: 5, key: "career_up_training", name: I18n.t("corporation.order.segment_ids.career_up_training")},
          {id: 6, key: "regular_order", name: I18n.t("corporation.order.segment_ids.regular_order")},
          {id: 7, key: "contract", name: I18n.t("corporation.order.segment_ids.contract")}
        ]
        expect(Order.order_segment_options).to eq(expected_options)
      end
    end

    context '#regular_order_segment_options' do
      it 'returns regular order segment options' do
        expected_options = [
          {id: 6, key: "regular_order", name: I18n.t("corporation.order.segment_ids.regular_order")}
        ]
        expect(Order.regular_order_segment_options).to eq(expected_options)
      end
    end
  end
end
