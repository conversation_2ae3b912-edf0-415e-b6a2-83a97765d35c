require "rails_helper"

RSpec.describe JobPageView, type: :model do
  let(:job_page_view) { build(:job_page_view) }

  describe "Associations" do
    it { should belong_to(:order_case) }
  end

  describe "soft delete" do
    it "acts as paranoid" do
      job_page_view = create(:job_page_view)
      job_page_view.destroy
      expect(JobPageView.with_deleted.find(job_page_view.id)).to eq(job_page_view)
      expect(JobPageView.find_by(id: job_page_view.id)).to be_nil
    end
  end
end
