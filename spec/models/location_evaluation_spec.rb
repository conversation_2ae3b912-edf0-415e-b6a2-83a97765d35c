require "rails_helper"

RSpec.describe LocationEvaluation, type: :model do
  let(:location_evaluation) { build(:location_evaluation) }

  describe "Constants" do
    it "has correct EVALUATION_RANGE" do
      expect(LocationEvaluation::EVALUATION_RANGE).to eq(1..5)
    end

    it "has correct UPDATE_ATTRS" do
      expected_attrs = %w(arrangement_id location_id evaluation evaluation_comment)
      expect(LocationEvaluation::UPDATE_ATTRS).to eq(expected_attrs)
    end

    it "has correct EVALUATION_ATTRS" do
      expected_attrs = %w(evaluation evaluation_comment)
      expect(LocationEvaluation::EVALUATION_ATTRS).to eq(expected_attrs)
    end
  end

  describe "Validators" do
    it { should validate_presence_of(:location_id) }
    it { should validate_presence_of(:staff_id) }
    it { should validate_presence_of(:arrangement_id) }
    it { should validate_presence_of(:evaluation) }

    it { should validate_numericality_of(:evaluation)
      .only_integer
      .is_greater_than_or_equal_to(1)
      .is_less_than_or_equal_to(5) }

    context 'evaluation range validation' do
      it "accepts valid evaluation scores" do
        (1..5).each do |score|
          evaluation = build(:location_evaluation, evaluation: score)
          evaluation.evaluation_comment = "Comment" if [1, 5].include?(score)
          expect(evaluation).to be_valid
        end
      end

      it "rejects evaluation scores outside range" do
        [6, 10].each do |score|
          evaluation = build(:location_evaluation, evaluation: score)
          expect(evaluation).not_to be_valid
          expect(evaluation.errors.details[:evaluation]).to include(a_hash_including(error: :less_than_or_equal_to))
        end

        [0, -1].each do |score|
          evaluation = build(:location_evaluation, evaluation: score)
          expect(evaluation).not_to be_valid
          expect(evaluation.errors.details[:evaluation]).to include(a_hash_including(error: :greater_than_or_equal_to))
        end
      end

      it "rejects non-integer evaluation scores" do
        evaluation = build(:location_evaluation, evaluation: 3.5)
        expect(evaluation).not_to be_valid
        expect(evaluation.errors.details[:evaluation]).to include(a_hash_including(error: :not_an_integer))
      end
    end

    context "when evaluation is 1" do
      it "validates presence of evaluation_comment" do
        evaluation = build(:location_evaluation, evaluation: 1, evaluation_comment: nil)
        expect(evaluation).not_to be_valid
        expect(evaluation.errors.details[:evaluation_comment]).to include(error: :blank)
      end
    end

    context "when evaluation is 5" do
      it "validates presence of evaluation_comment" do
        evaluation = build(:location_evaluation, evaluation: 5, evaluation_comment: nil)
        expect(evaluation).not_to be_valid
        expect(evaluation.errors.details[:evaluation_comment]).to include(error: :blank)
      end
    end

    context "when evaluation is 2, 3, or 4" do
      it "does not require evaluation_comment" do
        [2, 3, 4].each do |score|
          evaluation = build(:location_evaluation, evaluation: score, evaluation_comment: nil)
          expect(evaluation).to be_valid
        end
      end
    end
  end

  describe "Associations" do
    it { should belong_to(:location) }
    it { should belong_to(:staff) }
    it { should belong_to(:arrangement) }
  end

  describe "soft delete" do
    it "acts as paranoid" do
      evaluation = create(:location_evaluation)
      evaluation.destroy
      expect(LocationEvaluation.with_deleted.find(evaluation.id)).to eq(evaluation)
      expect(LocationEvaluation.find_by(id: evaluation.id)).to be_nil
    end
  end
end
