require "rails_helper"

RSpec.describe LocationEvaluationSummary, type: :model do
  describe "Validators" do
  end

  describe "Associations" do
    it{should belong_to(:location)}
  end

  describe "Scopes" do
    describe ".last_updated" do
      it "orders by updated_at desc" do
        old_summary = create(:location_evaluation_summary, updated_at: 1.day.ago)
        new_summary = create(:location_evaluation_summary, updated_at: 1.hour.ago)

        expect(LocationEvaluationSummary.last_updated).to eq([new_summary, old_summary])
      end
    end
  end
end
