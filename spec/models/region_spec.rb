require "rails_helper"

RSpec.describe Region, type: :model do
  let(:region) { build(:region) }


  describe "Associations" do
    it { should have_many(:prefectures) }
    it { should have_many(:locations).through(:prefectures) }
    it { should have_many(:locations_with_selects).through(:prefectures) }
  end

  describe "soft delete" do
    it "acts as paranoid" do
      region = create(:region)
      region.destroy
      expect(Region.with_deleted.find(region.id)).to eq(region)
      expect(Region.find_by(id: region.id)).to be_nil
    end
  end

  describe "instance methods" do
    let(:region) { create(:region) }
    let!(:prefecture) { create(:prefecture, region: region) }
    let!(:location) { create(:location, prefecture: prefecture) }

    it "can access locations through prefectures" do
      expect(region.locations).to include(location)
    end

    it "can access locations_with_selects through prefectures" do
      expect(region.locations_with_selects).to include(location)
    end
  end

  describe "locations_with_selects scope" do
    let(:region) { create(:region) }
    let!(:prefecture) { create(:prefecture, region: region) }
    let!(:location) { create(:location, prefecture: prefecture) }

    it "selects only specific fields" do
      locations = region.locations_with_selects
      expect(locations.first.attributes.keys).to include("id", "name", "postal_code", "prefecture_id")
    end
  end
end
