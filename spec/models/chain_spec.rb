require "rails_helper"

RSpec.describe Chain, type: :model do
  let(:chain) { build(:chain) }

  describe "Associations" do
    it { should have_many(:corporation_groups) }
    it { should have_many(:orders).through(:corporation_groups) }
    it { should have_many(:order_cases).through(:orders) }
    it { should have_many(:arrangements).through(:order_cases) }
  end

  describe "soft delete" do
    it "acts as paranoid" do
      chain = create(:chain)
      chain.destroy
      expect(Chain.with_deleted.find(chain.id)).to eq(chain)
      expect(Chain.find_by(id: chain.id)).to be_nil
    end
  end

  describe "class methods" do
    describe ".options_for_select" do
      let!(:chain1) { create(:chain, name: "Chain A") }
      let!(:chain2) { create(:chain, name: "Chain B") }

      it "returns array of name and id pairs" do
        options = Chain.options_for_select
        expect(options).to include(["Chain A", chain1.id])
        expect(options).to include(["Chain B", chain2.id])
      end

      it "returns correct format for select options" do
        options = Chain.options_for_select
        expect(options).to be_an(Array)
        expect(options.first).to be_an(Array)
        expect(options.first.size).to eq(2)
      end
    end
  end

  describe "instance methods" do
    let(:chain) { create(:chain) }
    let!(:corporation_group) { create(:corporation_group, chain: chain) }
    let!(:order) { create(:order, corporation_group: corporation_group) }
    let!(:order_case) { create(:order_case, order: order) }
    let!(:arrangement) { create(:arrangement, order_case: order_case) }

    it "can access corporation_groups" do
      expect(chain.corporation_groups).to include(corporation_group)
    end

    it "can access orders through corporation_groups" do
      expect(chain.orders).to include(order)
    end

    it "can access order_cases through orders" do
      expect(chain.order_cases).to include(order_case)
    end

    it "can access arrangements through order_cases" do
      expect(chain.arrangements).to include(arrangement)
    end
  end
end
